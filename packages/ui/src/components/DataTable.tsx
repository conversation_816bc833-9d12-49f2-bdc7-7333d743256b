import { FC, ReactElement, useEffect, useState, ReactNode, useMemo, useRef } from 'react'
import {
  capitalizeFirstLetter,
  formatDate,
  formatDateTime,
  formatMoney,
  useViewport,
  Sorting,
  formatPrice,
  formatDecimal,
} from 'utils'

import { FieldErrors } from '../models'

import { EmptyView, EmptyViewProps } from './EmptyView'
import { LoadingSection } from './LoadingSection'
import { ErrorSection } from './ErrorSection'
import { env } from 'process'

import classnames from 'classnames'
import { unionBy } from 'lodash'
import { ParsedUrlQuery } from 'querystring'

import { Button, Variant } from './Button'
import { v4 as uuidv4 } from 'uuid'
import { defaultValue } from '@/utils'
import { ErrorView } from './ErrorView'
import { DefaultSpinner } from './Spinner'

export interface DataTableField {
  name: string
  label: string
  isHTML?: boolean
  disableSort?: boolean
  sortFieldName?: string
  styles?: any
  format?: 'date' | 'datetime' | 'currency' | 'number' | 'price' | 'decimal' | 'array'
  formatter?: (item: any, index: number) => ReactNode
  renderType?: 'text' | 'image'
  childField?: string
  headerStyles?: string
  bodyStyles?: string
  editableFields?: EditableField
  hidden?: boolean
  isUnique?: boolean
  hideGroupHeaderField?: boolean
  ignoreOnClickRow?: boolean
  width?: string
  sticky?: boolean
  stickyDirection?: 'left' | 'right'
  cls?: string
  textAlignment?: string
  headerAlignment?: string
}

export interface LoadingView {
  size?: string
  color?: string
  label?: string
  isLoading?: boolean
}

export interface DataTableItem {
  id?: string
  [key: string]: any
  name?: string
  elementItems?: any[]
}

interface ErrorView {
  title?: string
  message?: string
  onRefresh?: (queryContext?: ParsedUrlQuery) => Promise<void> | void
  refreshLabel?: string
}

export interface EditableField {
  type: 'text' | 'number' | 'checkbox'
  placeholder?: string
  readonly?: boolean
  errorMsgParam?: Record<any, string>
}

export interface Selector {
  selectable?: 'single' | 'multi'
  selectArea?: 'choice' | 'row'
  onSelect?: (values: DataTableItem[]) => void
  disableSelector?: (item: DataTableItem) => boolean
  disableAllSelector?: boolean
  selectedValues?: DataTableItem[]
  hideSelectAll?: boolean
  sticky?: boolean
  isHaveErrorSelect?: boolean
}

interface ActionButton {
  text: string
  icon?: string
  variant: string | undefined
  ignoreHover: boolean
}
interface Action {
  onClickAction?: (item: DataTableItem) => void
  actionStyle?: any
  hideAction?: boolean
  headerActionStyle?: string
  sticky?: boolean
  actionButton?: (status: string) => ActionButton
  actionHeader?: string
}
export interface DataTableProps {
  fields: DataTableField[]
  t: (message: string, obj?: Record<string, any>) => string
  loadingView?: LoadingView
  emptyView?: EmptyViewProps
  errorView?: ErrorView
  data: DataTableItem[]
  errors?: Record<string, FieldErrors>
  isLoading?: boolean
  isEmptyView?: boolean
  isError?: boolean
  toggleSort?: (sortFieldName: string) => void
  sorting?: Sorting
  onClickRow?: (item: DataTableItem) => void
  heightOverScreen?: boolean
  onEditField?: (fieldName: string, index: number, value: string) => void
  onBlur?: (fieldName: string, index: number, value: string) => void
  dataOverride?: any[]
  cls?: string
  isScrollable?: boolean
  isInnerModal?: boolean
  action?: Action
  selector?: Selector
  getId?: (item: DataTableItem) => string
  maxLine?: number // for zIndex (beware of over 40 and loading layout zindex)
  autoLayout?: boolean // when true, uses table-auto to let content determine column widths
}

export const DataTable: FC<Partial<DataTableProps>> = ({
  fields,
  data = [],
  t = () => '',
  errors = {},
  toggleSort,
  sorting,
  isLoading = false,
  loadingView,
  isEmptyView = false,
  isError = false,
  emptyView = {
    title: t('common.data-not-found'),
    message: t('common.please-try-again'),
    isError: false,
  },
  errorView = {
    title: t('common.sorry'),
    message: t('common.something-went-wrong'),
    refreshLabel: t('common.refresh'),
  },
  onClickRow,
  heightOverScreen = false,
  onEditField,
  onBlur,
  dataOverride = [],
  cls = '',
  isScrollable = true,
  isInnerModal = false,
  action,
  selector,
  getId = (item: DataTableItem) => item.id,
  maxLine = 10,
  autoLayout = false,
}) => {
  const [isMounted, setIsMounted] = useState<boolean>(false)
  const tableElmRef = useRef<HTMLTableElement | null>(null)
  const { isMobile } = useViewport()
  useEffect(() => {
    setIsMounted(true)
  }, [])

  const {
    title: emptyTitle,
    content: emptyContent,
    message: emptyMessage,
    icon: emptyIcon,
    isError: isEmptyError,
    hideIcon: emptyViewHideIcon,
    image: emptyViewImage,
  } = emptyView
  const { title: errorTitle, message: errorMessage, onRefresh, refreshLabel: refreshLabel } = errorView

  const {
    selectable,
    selectArea = 'row',
    disableAllSelector = false,
    selectedValues = [],
    disableSelector,
    onSelect,
    hideSelectAll = false,
    sticky: stickySelector = false,
    isHaveErrorSelect = false,
  } = selector ?? {}

  const { actionStyle, hideAction = false, headerActionStyle = '', sticky: stickyAction = false } = action ?? {}

  // Single Select and Multi Select
  const onSelectAll = () => {
    const disableSelectorList: DataTableItem[] = disableSelector ? data.filter(item => disableSelector?.(item)) : []

    if (!isSelectAll) {
      // Select

      // Get deselect list from disable selector list
      const deselectList = disableSelectorList.filter(
        item => !selectedValues?.some(targetItem => getId(item) === getId(targetItem))
      )

      // Calculate selected list
      const selectedList = [...unionBy(data, selectedValues, getKeyByValue(data)), ...disableSelectorList]
      // Filter deselect list from selected list
      const filteredList = selectedList.filter(
        item => !deselectList.some(targetItem => getId(item) === getId(targetItem))
      )
      onSelect?.(filteredList)
    } else {
      // Deselect
      const selectedList = selectedValues?.filter(item => !data?.some(targetItem => getId(item) === getId(targetItem)))
      const disableSelectedList = disableSelectorList.filter(
        item => selectedValues?.some(targetItem => getId(item) === getId(targetItem))
      )
      onSelect?.([...(selectedList || []), ...disableSelectedList])
    }
  }

  const onSingleSelectRow = (selectedRow: DataTableItem) => {
    onSelect?.([selectedRow])
  }

  const onMultiSelectRow = (selectedRow: DataTableItem) => {
    if (isSelected(selectedRow)) {
      onSelect?.((selectedValues || []).filter(data => getId(data) !== getId(selectedRow)))
    } else {
      onSelect?.([...(selectedValues || []), selectedRow])
    }
  }

  const isSelected = (value: DataTableItem) => {
    return (selectedValues || []).filter(data => getId(data) === getId(value)).length > 0
  }

  const isSelectAll: boolean = useMemo(() => {
    const enableData = data.filter(item => !disableSelector?.(item))
    return enableData.length > 0 && enableData.every(item => isSelected(item))
  }, [selectedValues, data])

  const isMultiSelectable = useMemo(() => {
    return selectable == 'multi'
  }, [selectable])

  const isSingleSelectable = useMemo(() => {
    return selectable == 'single'
  }, [selectable])

  const onSelectRow = (item: DataTableItem) => {
    // Support only selector
    if (disableSelector?.(item)) {
      return
    }

    if (selectable == 'single') {
      onSingleSelectRow(item)
      return
    }

    if (selectable == 'multi') {
      if (disableAllSelector) {
        return
      }

      onMultiSelectRow(item)
    }
  }

  useEffect(() => {
    // Select table group
    const tableGroup = document.getElementById('table-group')
    // Prevent tablegroup undefined
    if (tableGroup && (isLoading || (data && !data.length))) {
      tableGroup.scrollLeft = 0
      tableGroup.scrollTop = 0
    }
  }, [isLoading, data?.length])

  const getItemId = (index: number, item: DataTableItem) => {
    const uniqfield = fields?.find(item => item.isUnique === true)
    const id = uniqfield ? (item as any)[uniqfield.name] : index
    return id.toString()
  }

  const getKeyByValue = (objList: DataTableItem[]): string | undefined => {
    for (const obj of objList) {
      const key = Object.keys(obj).find((key, index) => {
        return obj[key] === getId(obj)
      })
      if (key !== undefined) {
        return key
      }
    }
    return undefined
  }

  const renderEditableField = (field: DataTableField, index: number, item: DataTableItem) => {
    const isDisabled = field.editableFields?.readonly ?? false
    const placeholder = field.editableFields?.placeholder
    // Looks for error message based on id and field name
    const error = errors[getItemId(index, item)]?.[field.name]

    if (['text', 'number'].includes(field.editableFields?.type || 'text')) {
      return (
        <div className="relative block">
          <input
            type={field.editableFields?.type}
            name={`${field.name}-${index}`}
            value={(item as any)[field.name] ?? ''}
            onKeyUp={event => {
              event.preventDefault()
              if (event.key === 'Enter') {
                event.currentTarget.blur()
              }
            }}
            onBlur={e => onBlur?.(field.name, index, e.target.value)}
            onChange={e => onEditField?.(field.name, index, e.target.value)}
            className={`text-ellipsis rounded-lg border py-2.5 px-4 w-full focus:border-primary-500 focus:outline-none placeholder:text-base-400 text-base-800 focus:text-base-700 h-11 read-only:disabled:text-base-400 read-only:disabled:bg-base-100 ${
              error ? 'border-negative-500 field-error' : 'border-base-200'
            }`}
            disabled={isDisabled}
            placeholder={placeholder}
          />
        </div>
      )
    } else if (field.editableFields?.type === 'checkbox') {
      return (
        <div className="w-[20px] text-primary-gray group-hover:bg-base-50 text-lu4-regular bg-white border-[#DBDBDB]">
          <p
            key="all"
            className={`flex cursor-pointer ${disableSelector?.(item) || disableAllSelector ? '!cursor-default' : ''}`}
          >
            <span
              className={`items-center justify-center flex rounded-md w-5 min-w-[20px] h-5 border ${
                (item as any)[field.name] ? 'border-primary-400 bg-primary-400' : 'bg-white border-base-300'
              } ${disableSelector?.(item) || disableAllSelector ? '!border-base-300 !bg-base-200' : ''}`}
              onClick={e => {
                if (!disableSelector?.(item)) {
                  onEditField?.(field.name, index, (item as any)[field.name] ? '' : 'active')
                }
              }}
            >
              {(item as any)[field.name] && <i className={`icons ic-checked-white w-[9px] h-[7px]`} />}
            </span>
          </p>
        </div>
      )
    }
  }

  const isOverrideColumn = (field: DataTableField, index: number) => {
    if (dataOverride.filter(d => d.index === index && d.fieldName === field.name).length) {
      return true
    }
    return false
  }

  const overrideColumn = (field: DataTableField, index: number) => {
    if (isOverrideColumn(field, index)) {
      return dataOverride.filter(d => d.index === index && d.fieldName === field.name)[0].data ?? ''
    }
    return ''
  }

  const getIndexShadowBox = () => {
    if (fields) return fields.findIndex(item => item.sticky === true && item.stickyDirection === 'right')
    return -1
  }

  const getStickyStyle = (
    position: 'left' | 'right' = 'left',
    sticky?: boolean,
    index?: number
  ): React.CSSProperties => {
    if (!sticky) return {}

    let pos = 0

    const boxShadow = ``
    if (index !== undefined && position === 'left') {
      const leftPos = getLeftStickyPos(index)
      pos = stickySelector ? leftPos + 50 : leftPos
    }
    if (index !== undefined && position === 'right') {
      const rightPos = getRightStickyPos(index)
      pos = !hideAction ? rightPos + 150 : rightPos
    }

    return {
      position: 'sticky',
      [position]: `${pos}px`,
      zIndex: 5,
      boxShadow,
    }
  }

  const getLeftStickyPos = (index: number) =>
    fields?.reduce((acc, field, i) => {
      return i < index && field.sticky ? acc + parseInt(defaultValue(field.width?.slice(0, -2), '0')) : acc
    }, 0) ?? 0

  const getRightStickyPos = (index: number) =>
    fields?.reduce((acc, field, i) => {
      return i > index && field.sticky ? acc + parseInt(defaultValue(field.width?.slice(0, -2), '0')) : acc
    }, 0) ?? 0

  const getColumnStyle = (item: any) => {
    const cursorState = () => {
      if (onClickRow) {
        return 'pointer'
      }

      if (selectable) {
        if (!disableSelector?.(item)) {
          return 'pointer'
        }
      }
      return 'default'
    }
    return {
      cursor: cursorState(),
    }
  }

  const dataTableStyle = (
    heightOverScreen: boolean,
    isScrollable: boolean,
    data: DataTableItem[],
    isLoading: boolean,
    isError: boolean,
    isEmptyView: boolean
  ): string => {
    // Check table scrollable props and data length more than equal 5 items
    const scrollableWithMajorData = isScrollable && data.length >= 5

    // Return concat classname string
    return classnames('overflow-x-auto relative md:h-auto', {
      'h-[400px] min-h-[400px] !overflow-hidden my-auto flex items-center justify-center':
        isLoading || isEmptyView || isError,
      'h-[400px]': isScrollable,
      'md:h-auto': !isScrollable || heightOverScreen,
      'md:basis-0 md:flex-grow': scrollableWithMajorData,
      'md:basis-[min-content]': !isScrollable,
      'md:basis-[600px]': scrollableWithMajorData && heightOverScreen,
      'md:h-0': scrollableWithMajorData && !heightOverScreen,
      'h-auto !min-h-full': isInnerModal,
      'overscroll-none': isMobile && isScrollable,
    })
  }

  const renderActionMenu = (item: DataTableItem, actionButtonFunction?: (status: string) => ActionButton) => {
    const actionButton = actionButtonFunction
      ? actionButtonFunction(item.status)
      : { text: 'common.data-table.action-detail', icon: 'ic-show-hide-blue', variant: 'outlined', ignoreHover: false }
    return (
      <>
        <Button
          colorScheme="primary"
          variant={actionButton.variant as Variant}
          leftIcon={
            actionButton.icon ? <i className={`icons ${actionButton.icon} !w-[16px] !h-[16px] mr-2`}></i> : undefined
          }
          onClick={() => action?.onClickAction && action?.onClickAction(item)}
          padding="!px-4 !py-1.5"
          cls={`h-[32px] w-max ${actionButton.ignoreHover ? '' : 'group-hover:bg-base-50'} ${actionStyle?.cls}`}
          size="xs"
        >
          {t(`${actionButton.text}`)}
        </Button>
      </>
    )
  }

  const renderConditionalSection = (isError: boolean, isEmptyView: boolean, isLoading: boolean) => {
    switch (true) {
      case isError:
        return (
          <div className="flex items-center justify-center">
            <ErrorView title={errorTitle} message={errorMessage} onRefresh={onRefresh} refreshLabel={refreshLabel} />
          </div>
        )
      case isEmptyView:
        return (
          <div className="flex items-center justify-center">
            <EmptyView
              title={emptyTitle}
              content={emptyContent}
              message={emptyMessage}
              icon={emptyIcon}
              isError={isEmptyError}
              hideIcon={emptyViewHideIcon}
              image={emptyViewImage}
            />
          </div>
        )
      case isLoading:
        return (
          <div className="flex items-center justify-center">
            <DefaultSpinner label={loadingView?.label} />
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div
      className={`${dataTableStyle(
        heightOverScreen,
        isScrollable,
        data || [],
        isLoading,
        isError,
        isEmptyView
      )} ${cls} `}
      id="table-group"
    >
      <div ref={tableElmRef} id="data-table" style={{ minHeight: 'auto' }}>
        {renderConditionalSection(isError, isEmptyView, isLoading) ?? (
          <table
            className={`table w-full overflow-x-auto overflow-y-visible ${
              autoLayout ? 'table-auto' : 'table-fixed'
            } md:h-full`}
          >
            <thead className="sticky top-0" style={{ zIndex: maxLine + 1 }}>
              <tr>
                {isSingleSelectable && (
                  <th
                    className="w-[50px] px-4 py-[10px] bg-primary-50 h-11 border-b-[2px] after:inline cursor-pointer"
                    style={getStickyStyle('left', stickySelector)}
                  ></th>
                )}
                {isMultiSelectable && (
                  <th
                    className="w-[50px] px-4 py-[10px] bg-primary-50 h-11 border-b-[2px] !border-b-[#dbdbdb]"
                    style={getStickyStyle('left', stickySelector)}
                  >
                    <p
                      key="all"
                      className={`flex items-center justify-center cursor-pointer ${
                        disableAllSelector ? '!cursor-default' : ''
                      }
                      ${hideSelectAll ? 'display-none' : ''}
                      `}
                      onClick={() => (disableAllSelector ? () => {} : onSelectAll())}
                    >
                      <span
                        className={`items-center justify-center flex rounded-md w-5 min-w-[20px] h-5 border p-1 ${
                          isSelectAll ? 'border-primary-500 bg-primary-500' : 'bg-white border-base-500'
                        } ${disableAllSelector ? '!border-base-300 !bg-base-200' : ''}
                        ${isHaveErrorSelect && 'bg-white border-red-500'}`}
                      >
                        {isSelectAll && <i className={`icons ic-checked-white w-[9px] h-[7px]`} />}
                      </span>
                    </p>
                  </th>
                )}
                {fields
                  ?.filter(f => {
                    return !f.hidden
                  })
                  .map((field, i) => (
                    <th
                      className={`px-4 text-primary-400 py-[10px] bg-primary-50 h-11 border-b-[2px] after:inline cursor-pointer whitespace-pre
                      ${field.headerStyles} ${getIndexShadowBox() === i ? 'th-shadow' : ''}`}
                      key={`${field.name}-${field.label}`}
                      onClick={() => {
                        if (field.disableSort) return
                        if (toggleSort) {
                          toggleSort(field.sortFieldName ? field.sortFieldName : field.name)
                        }
                      }}
                      style={{
                        ...getStickyStyle(field.stickyDirection, field.sticky, i),
                        ...(field.width && field.width !== '100%' ? { width: field.width } : {}),
                        ...(field.width === '100%' && autoLayout ? { width: 'auto', minWidth: '100%' } : {}),
                        ...(field.width === '100%' && !autoLayout ? { width: field.width } : {}),
                      }}
                    >
                      <span
                        className={`flex items-center text-t5-semi-bold ${
                          field.headerAlignment || field.textAlignment
                        }`}
                      >
                        {capitalizeFirstLetter(field.label)}
                        {isMounted && getSortingIcon(field, sorting)}
                      </span>
                    </th>
                  ))}
                {!hideAction && (
                  <th
                    className={` border-b-[2px] text-primary-400 py-[10px] bg-primary-50 w-[150px] ${
                      getIndexShadowBox() === -1 && stickyAction ? 'th-shadow' : ''
                    }
                    ${headerActionStyle}`}
                    style={getStickyStyle('right', stickyAction)}
                  >
                    {action?.actionHeader ? action.actionHeader : t('common.action')}
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="z-0">
              {data.map((item, itemIndex) => (
                <tr
                  key={uuidv4()}
                  className="group hover:bg-base-50"
                  onClick={() => {
                    if (selectArea == 'row') {
                      onSelectRow(item)
                    }
                  }}
                >
                  {/* Single Selectable */}
                  {isSingleSelectable && (
                    <td
                      className="w-[50px] !p-0 border-b-[2px] px-4 text-primary-gray group-hover:bg-base-50 text-lu4-regular bg-white
                              border-[#DBDBDB]"
                      style={getStickyStyle('left', stickySelector)}
                    >
                      <div
                        className={`flex justify-center items-center h-11 ${
                          !disableSelector?.(item) && 'cursor-pointer'
                        }`}
                      >
                        <div
                          className={`rounded-full w-5 h-5 border ${
                            disableSelector?.(item) ? 'border-base-300 bg-white' : 'border-base-200'
                          }  
                          ${
                            isSelected(item) &&
                            `border-[6px] ${disableSelector?.(item) ? 'border-base-200' : 'border-primary-400'}`
                          }
                          `}
                          onClick={() => {
                            if (selectArea == 'choice') {
                              onSelectRow(item)
                            }
                          }}
                        ></div>
                      </div>
                    </td>
                  )}
                  {/* Multi Selectable */}
                  {isMultiSelectable && (
                    <td
                      className="w-[50px] border-b-[2px] px-4 text-primary-gray group-hover:bg-base-50 text-lu4-regular bg-white
                        border-[#DBDBDB]"
                      style={getStickyStyle('left', stickySelector)}
                    >
                      <p
                        key="all"
                        className={`flex items-center justify-center cursor-pointer ${
                          disableSelector?.(item) || disableAllSelector ? '!cursor-default' : ''
                        }`}
                        onClick={() => {
                          if (selectArea == 'choice') {
                            onSelectRow(item)
                          }
                        }}
                      >
                        <span
                          className={`items-center justify-center flex rounded-md w-5 min-w-[20px] h-5 border p-1 ${
                            isSelected(item) ? 'border-primary-500 bg-primary-500' : 'bg-white border-base-500'
                          } ${disableSelector?.(item) || disableAllSelector ? '!border-base-300 !bg-base-200' : ''} 
                          ${isHaveErrorSelect && 'bg-white border-red-500'}`}
                        >
                          {isSelected(item) && <i className={`icons ic-checked-white w-[9px] h-[7px]`} />}
                        </span>
                      </p>
                    </td>
                  )}

                  {fields
                    ?.filter(f => {
                      return !f.hidden
                    })
                    .map((field, index) => {
                      const { name, label } = field
                      return (
                        <td
                          key={`${name}-${label}`}
                          className={`border-b-[2px] px-4 text-base-700 group-hover:bg-base-50 text-lu4-regular bg-white
                            border-[#DBDBDB] h-[58px] max-h-[58px] ${field.width === '100%' && autoLayout ? '' : 'truncate'}  ${field.bodyStyles}
                            ${onClickRow && 'group-hover:cursor-pointer'} ${
                              getIndexShadowBox() === index ? 'td-shadow' : ''
                            } ${field.textAlignment}`}
                          onClick={!field.ignoreOnClickRow ? () => onClickRow?.(item) : undefined}
                          style={{
                            zIndex: maxLine - itemIndex,
                            overflowY: 'visible',
                            ...getColumnStyle(item),
                            ...getStickyStyle(field.stickyDirection, field.sticky, index),
                            ...(field.width && field.width !== '100%' ? { width: field.width } : {}),
                            ...(field.width === '100%' && autoLayout ? { width: 'auto', minWidth: '100%' } : {}),
                            ...(field.width === '100%' && !autoLayout ? { width: field.width } : {}),
                          }}
                        >
                          {field.editableFields && <>{renderEditableField(field, itemIndex, item)}</>}
                          {!field.editableFields && isOverrideColumn(field, itemIndex) && (
                            <>{overrideColumn(field, itemIndex)}</>
                          )}
                          {!field.editableFields && !isOverrideColumn(field, itemIndex) && (
                            <span className={field.cls}>
                              {getValue(field, item, itemIndex)}
                              {field.childField && getChildItems(field.childField, item)}
                            </span>
                          )}
                        </td>
                      )
                    })}
                  {!hideAction && (
                    <>
                      <td
                        key={`${item.id}-${item.name}-${uuidv4()}`}
                        className={`w-full bg-white group-hover:bg-base-50 border-b-[2px] border-[#DBDBDB] ${
                          getIndexShadowBox() === -1 && stickyAction ? 'td-shadow' : ''
                        }`}
                        style={{
                          ...getStickyStyle('right', stickyAction),
                        }}
                      >
                        <div className="relative flex items-center justify-center">
                          {renderActionMenu(item, action?.actionButton)}
                        </div>
                      </td>
                    </>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  )
}

const getSortingIcon = (field: DataTableField, sorting?: Sorting) => {
  if (field.disableSort) return

  const sortFieldName = field.sortFieldName ?? field.name
  let icon = 'ic-sort'

  if (sorting?.by === sortFieldName) {
    if (sorting?.direction === 'desc') icon = 'ic-sort-down'
    if (sorting?.direction === 'asc') icon = 'ic-sort-up'
  }

  return <i className={`icons ${icon} !w-[16px] !h-[16px] ml-[2px] mb-[2px] cursor-pointer inline-table`} />
}

const getValue = (field: DataTableField, item: DataTableItem, index: number) => {
  const value = (item as any)[field.name]

  if (field.formatter) {
    return field.formatter(item, index) ?? '-'
  }

  switch (field.format) {
    case 'date':
      return defaultValue(formatDate(value, env.dateFormat ?? 'dd/MM/yyyy'))
    case 'datetime':
      return defaultValue(formatDateTime(value))
    case 'currency':
      return defaultValue(formatMoney(value, 2))
    case 'number':
      return defaultValue(formatMoney(value, 0))
    case 'price':
      return defaultValue(formatPrice(value, 2))
    case 'decimal':
      return defaultValue(formatDecimal(value))
    case 'array':
      return (
        <div className="flex flex-row truncate">
          {value.map((data: string) => {
            return (
              <p key={`${data}-${uuidv4()}`} className="after:content-[','] after:last:content-[''] pl-1">
                {data}
              </p>
            )
          }) ?? '-'}
        </div>
      )
    default:
      return value ?? '-'
  }
}

const getChildItems = (childField: string, item: any) => {
  return <p className="text-b6-regular text-[#7A7A7A]">{item[childField] && capitalizeFirstLetter(item[childField])}</p>
}

// ---  GetMeResponse --- //
export interface GetMeResponse {
  userKey: string
  name: string
  branchRoles: BranchRole[]
  roleConfig: RoleConfig[] // user specific
  permissionGroupConfig: PermissionGroupConfig[]
  userType: string
  vendorType: string | null
}

export interface BranchRole {
  branchId: string
  branchName: string
  roles: string[]
}

export interface RoleConfig {
  roleId: string
  rolePermissions: RolePermission[]
}

export interface RolePermission {
  permissionId: string
  view: boolean
  create: boolean
  update: boolean
  delete: boolean
  download: boolean
  upload: boolean
}

export interface PermissionGroupConfig {
  permissionGroupId: string
  label: string
  isInMenu: boolean
  permissions: PermissionDetail[]
}

export interface PermissionDetail {
  permissionId: string
  label: string
  iconName: string // inactive
  iconNameActive: string // active
}

// --- --- //

export enum AdminRole {
  SUPER_ADMIN = 'SuperAdmin',
  ADMIN = 'Admin',
  PRICE_ESTIMATOR = 'AdminPriceEstimator',
  RECEIVE = 'AdminReceive',
  QC = 'AdminQC',
  REPAIR = 'AdminRepair',
  INSPECTION = 'AdminInspection',
  SUPPLY_CHAIN = 'AdminSupplyChain',
  PRODUCT = 'AdminProduct',
  MARKETING = 'AdminMarketing',
  RCC = 'AdminRCC',
}

export interface GetStaffTableResponse {
  updatedAt: Date
  name: string
  userKey: string
  roles: string[]
  branchIds?: string[]
  branchTitles?: string[]
}

export interface CustomUserQueryResult<UserRoleBranch> {
  userKey: string
  name: string
  nameEng: string
  updatedAt: string
  //roles: UserRoles
  userRoleBranchs: UserRoleBranch[]
}

export interface UserItems {
  userKey: string
  name: string
  nameEng: string
  updatedAt: string
  branchIds: string[]
  branchTitles: string[]
  roles: string[]
}

export interface GetUsersRequest {
  searchEmployee?: string
  role?: string
  branch?: string
  page?: string
  pageSize?: string
  pagination?: string
  orderBy?: string
}

export interface GetUsersResponse {
  items: UserItems[]
}

export interface GetRoleConfigItemResponse {
  roleId: string
  roleName: string
  type: 'CMS' | 'FRONTSHOP'
}

interface BranchDetail {
  branchId: string
  branchName: string
}

export interface UserRolesByType {
  frontshop: {
    roleId: string
    branch: BranchDetail
  }[]
  cms: {
    roleId: string
  }[]
}

export interface GetStaffConfigResponse {
  userKey: string
  name: string
  lastname: string
  nameEn: string
  lastnameEn: string
  roles: UserRolesByType
  email: string
}

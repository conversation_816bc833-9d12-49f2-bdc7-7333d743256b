import {
  JobActivitiesEntity,
  JobStatus,
  UserRoleBranchEntity,
} from '../entities';
import { FirebaseService } from '../firebase/firebase.service';
import {
  DataSource,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
} from 'typeorm';
import { DataChangeSlug } from './job.subscriber';
import { makeCollectionPath, makeJobData } from './helper';
import { Permission, PermissionAction } from '../config';

// ** Activity type for decision collection path firestore **
export enum ActivitiesType {
  // Path: `company/${entity.companyId}/conversation/${entity.jobId}/activity`;
  CONVERSATION = 'conversation',

  // Path: `company/${entity.companyId}/inbox/${entity.detail.branchId}/message`;
  INBOX = 'inbox',

  // Path: `company/${entity.companyId}/voucher-conversation/${entity.voucherId}/activity`;
  VOUCHER_CONVERSATION = 'voucher_conversation',

  // Path: `company/${entity.companyId}/do-conversation/${entity.deliveryOrderId}/activity`;
  DO_CONVERSATION = 'do_conversation',

  // Path: `company/${entity.companyId}/imported-voucher-conversation/${id}/activity`
  IMPORTED_VOUCHER_CONVERSATION = 'imported_voucher_conversation',

  CAMPAIGN_REDEMPTION_CODE_CONVERSATION = 'campaign_redemption_code_conversation',
}

// ** Inbox type for represent type of inbox to ui **
export enum InboxType {
  // Admin comment
  ADMIN_FEEDBACK = 'admin_feedback',

  // Shop comment
  SHOP_FEEDBACK = 'shop_feedback',

  // Job create
  JOB_CREATE = 'job_create',

  // Job assigned
  ESTIMATE_PROCESSING = 'estimate_processing',

  // Job estimated
  ESTIMATE_COMPLETE = 'estimate_complete',

  // Identity requested
  IDENTITY_REQUESTED = 'identity_request',

  // Identity verified
  IDENTITY_VERIFIED = 'identity_verify',

  // Identity rejected
  IDENTITY_REJECTED = 'identity_reject',

  // Voucher requested
  VOUCHER_REQUESTED = 'voucher_requested',

  // Delivery order requested
  DELIVERY_ORDER_REQUESTED = 'delivery_order_requested',

  // Delivery order shipped
  DELIVERY_ORDER_SHIPPED = 'delivery_order_shipped',

  // Delivery order confirmed
  DELIVERY_ORDER_CONFIRMED = 'delivery_order_confirmed',

  // Delivery order completed
  DELIVERY_ORDER_SUCCCESS = 'delivery_order_success',

  // Imported Voucher notificaiton
  IMPORTED_VOUCHER_OUT_OF_STOCK = 'imported_voucher_out_of_stock',
  // Issue report approved
  ISSUE_REPORT_APPROVED = 'issue_report_approved',

  // Issue report rejected
  ISSUE_REPORT_REJECTED = 'issue_report_rejected',

  // Issue report requested
  ISSUE_REPORT_REQUESTED = 'issue_report_requested',
}

// ** Audience type for decision inbox subscriber firestore frontend **
export enum AudienceType {
  // All shop in branch ex. shop_all_branch
  SHOP_BRANCH = 'shop_all_branch',

  // Role manager only
  MANAGER_ONLY = 'manager_only',

  // Employee only ex. employee_only:{userKey}
  EMPLOYEE_ONLY = 'employee_only',

  // Price estimator only ex. price_estimator:{userKey}
  PRICE_ESTIMATOR = 'price_estimator',

  // Admin B
  RECEIVE = 'receive',

  // Admin Marketing
  ADMIN_MARKETING = 'admin_marketing',
  // Admin RCC
  RCC = 'rcc',

  // All role
  ALL = 'all',
}

@EventSubscriber()
export class JobActivitiesEntitySubscriber
  implements EntitySubscriberInterface<JobActivitiesEntity>
{
  constructor(
    dataSource: DataSource,
    private readonly firebaseService: FirebaseService,
  ) {
    dataSource.subscribers.push(this);
  }

  listenTo() {
    return JobActivitiesEntity;
  }

  async afterInsert(event: InsertEvent<JobActivitiesEntity>): Promise<void> {
    // Get entity
    const entity = event.entity;

    // Prevent invalid entity
    if (!entity) return;

    // Type check job entity
    if (entity instanceof JobActivitiesEntity) {
      // Admin assigned
      if (entity.detail.dataChangeSlug === DataChangeSlug.ADMIN_ASSIGNED) {
        //AudienceType.EMPLOYEE_ONLY
        this.firebaseService.addData(
          makeCollectionPath(entity, ActivitiesType.INBOX),
          this.makeDataInbox(
            entity,
            InboxType.ESTIMATE_PROCESSING,
            Permission.SHOP_JOB_MY + PermissionAction.CREATE,
          ),
        );
        // Shop comment
      } else if (
        entity.detail.dataChangeSlug === DataChangeSlug.COMMENTED ||
        entity.detail.dataChangeSlug === DataChangeSlug.ADDITIONAL_IMAGE
      ) {
        //AudienceType.PRICE_ESTIMATOR UPDATE
        this.firebaseService.addData(
          makeCollectionPath(entity, ActivitiesType.INBOX, true),
          this.makeDataInbox(
            entity,
            InboxType.SHOP_FEEDBACK,
            Permission.CMS_JOB_MY + PermissionAction.UPDATE,
          ),
        );
        // Admin comment
        //AudienceType.EMPLOYEE_ONLY
      } else if (
        entity.detail.dataChangeSlug === DataChangeSlug.ADMIN_COMMENTED
      ) {
        this.firebaseService.addData(
          makeCollectionPath(entity, ActivitiesType.INBOX),
          this.makeDataInbox(
            entity,
            InboxType.ADMIN_FEEDBACK,
            Permission.SHOP_JOB_MY + PermissionAction.CREATE,
          ),
        );
        // Complete estimate price
        //AudienceType.EMPLOYEE_ONLY
      } else if (entity.detail.status === JobStatus.PRICE_ESTIMATED) {
        this.firebaseService.addData(
          makeCollectionPath(entity, ActivitiesType.INBOX),
          this.makeDataInbox(
            entity,
            InboxType.ESTIMATE_COMPLETE,
            Permission.SHOP_JOB_MY + PermissionAction.CREATE,
          ),
        );
      } else if (
        entity.detail.dataChangeSlug === DataChangeSlug.JOB_REQUESTED
      ) {
        //AudienceType.PRICE_ESTIMATOR UPDATE
        this.firebaseService.addData(
          makeCollectionPath(entity, ActivitiesType.INBOX, true),
          this.makeDataInbox(
            entity,
            InboxType.JOB_CREATE,
            Permission.CMS_JOB_ALL + PermissionAction.UPDATE,
          ),
        );
      } else if (
        entity.detail.dataChangeSlug === DataChangeSlug.IDENTITY_REQUESTED
      ) {
        const isRoleManager = await this.isRoleManager(event, entity);
        if (isRoleManager) {
          return;
        }
        //AudienceType.MANAGER_ONLY
        this.firebaseService.addData(
          makeCollectionPath(entity, ActivitiesType.INBOX),
          this.makeDataInbox(
            entity,
            InboxType.IDENTITY_REQUESTED,
            Permission.SHOP_OTHERS_OCR_CONFIRM + PermissionAction.UPDATE,
          ),
        );
      } else if (
        entity.detail.dataChangeSlug ===
          DataChangeSlug.IDENTITY_VERIFIED_DIPCHIP ||
        entity.detail.dataChangeSlug === DataChangeSlug.IDENTITY_VERIFIED
      ) {
        // If slug verify by dipchip should not send inbox
        if (
          entity.detail.dataChangeSlug ===
          DataChangeSlug.IDENTITY_VERIFIED_DIPCHIP
        ) {
          return;
        }

        const isRoleManager = await this.isRoleManager(event, entity);
        if (isRoleManager && entity.detail.shopUserKey === entity.createdBy) {
          return;
        }
        //AudienceType.EMPLOYEE_ONLY
        this.firebaseService.addData(
          makeCollectionPath(entity, ActivitiesType.INBOX),
          this.makeDataInbox(
            entity,
            InboxType.IDENTITY_VERIFIED,
            Permission.SHOP_JOB_MY + PermissionAction.CREATE,
          ),
        );
      } else if (
        entity.detail.dataChangeSlug === DataChangeSlug.IDENTITY_REJECTED
      ) {
        //AudienceType.EMPLOYEE_ONLY
        this.firebaseService.addData(
          makeCollectionPath(entity, ActivitiesType.INBOX),
          this.makeDataInbox(
            entity,
            InboxType.IDENTITY_REJECTED,
            Permission.SHOP_JOB_MY + PermissionAction.CREATE,
          ),
        );
      }

      this.firebaseService.addData(
        makeCollectionPath(entity, ActivitiesType.CONVERSATION),
        makeJobData(entity),
      );
    }
  }

  private makeAudience(audienceType: string, entity: JobActivitiesEntity) {
    //AudienceType.MANAGER_ONLY
    if (
      audienceType ===
      Permission.SHOP_OTHERS_OCR_CONFIRM + PermissionAction.UPDATE
    ) {
      return audienceType;
    }

    // Audience for employee only, ex. employee_only:{userKey}
    // AudienceType.EMPLOYEE_ONLY
    if (audienceType === Permission.SHOP_JOB_MY + PermissionAction.CREATE) {
      return `${audienceType}:${entity.detail.shopUserKey}`;
    }

    // Audience for price estimator(Admin A) only
    // AudienceType.PRICE_ESTIMATOR case no admin owner = quote_requested only
    if (audienceType === Permission.CMS_JOB_ALL + PermissionAction.UPDATE) {
      return `${audienceType}:${entity.detail.vendorType}`;
    }
    // AudienceType.PRICE_ESTIMATOR case with admin owner = [ estimate_price_processing ]
    if (audienceType === Permission.CMS_JOB_MY + PermissionAction.UPDATE) {
      return `${audienceType}:${entity.detail.adminUserKey}`;
    }

    return AudienceType.ALL;
  }

  private makeDataInbox(
    entity: JobActivitiesEntity,
    inboxType: InboxType,
    audience: string,
  ) {
    // Make data job firestore
    return {
      type: inboxType,
      jobId: entity.jobId,
      status: entity.detail.status,
      updatedAt: +(entity.updatedAt ?? 0),
      audience: this.makeAudience(audience, entity),
      shopUserName: entity.detail.shopUserName,
      adminUserName: entity.detail.adminUserName,
      shopUserKey: entity.detail.shopUserKey,
      adminUserKey: entity.detail.adminUserKey,
    };
  }

  private async isRoleManager(
    event: InsertEvent<JobActivitiesEntity>,
    entity: JobActivitiesEntity,
  ): Promise<boolean> {
    // Find user with manager role
    const userRoleBranch = await event.manager.findOne(UserRoleBranchEntity, {
      where: {
        companyId: entity.companyId,
        userKey: entity.detail.shopUserKey,
        branchId: entity.detail.branchId,
        role: {
          rolePermissions: {
            permissionId: 'PS-0030',
            update: true,
          },
        },
      },
      relations: ['role.rolePermissions'],
    });

    return userRoleBranch ? true : false;
  }
}

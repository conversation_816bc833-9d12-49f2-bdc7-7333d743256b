import { FirebaseService } from '../firebase/firebase.service';
import {
  DataSource,
  EntitySubscriberInterface,
  EventSubscriber,
  UpdateEvent,
} from 'typeorm';
import {
  JobActivitiesEntity,
  JobActivitiesType,
  JobEntity,
  JobStatus,
} from '../entities';
import { DataChangeInfo, deepDifference } from '../utils';
import { get, isEqual } from 'lodash';
import { makeJobData, makePendingJobPath } from './helper';

// ** Data change slug for decision event to firestore **
export enum DataChangeSlug {
  // Job requested to admin
  JOB_REQUESTED = 'job_requested',

  // Admin assigned to job
  ADMIN_ASSIGNED = 'admin_assigned',

  // Comment by shop
  COMMENTED = 'commented',

  // Admin by comment
  ADMIN_COMMENTED = 'admin_commented',

  // Common job update
  JOB_UPDATED = 'job_updated',

  // Add more additional image
  ADDITIONAL_IMAGE = 'additional_image',

  // Identity requested
  IDENTITY_REQUESTED = 'identity_requested',

  // Identity verified
  IDENTITY_VERIFIED = 'identity_verified',

  // Identity rejected
  IDENTITY_REJECTED = 'identity_rejected',

  // Identity verified with dipchip
  IDENTITY_VERIFIED_DIPCHIP = 'identity_verified_dipchip',
}

// ** Summary is described in overall event **
export enum Summary {
  // Job created
  JOB_CREATED = 'Job created',

  // Job assigned
  JOB_ASSIGNED = 'Job assigned',

  // Comment by shop
  COMMENTED = 'Commented',

  // Commnet by admin
  ADMIN_COMMENTED = 'Admin commented',

  // Common Job update
  JOB_UPDATED = 'Job updated',

  // Add more additional image
  ADDITIONAL_IMAGE = 'Additional Image',

  // Identity requested
  IDENTITY_REQUESTED = 'Request Identity',

  // Identity verified
  IDENTITY_VERIFIED = 'Verify Identity',

  // Identity rejected
  IDENTITY_REJECTED = 'Reject Identity',
}

@EventSubscriber()
export class JobEntitySubscriber
  implements EntitySubscriberInterface<JobEntity>
{
  constructor(
    dataSource: DataSource,
    private readonly firebaseService: FirebaseService,
  ) {
    dataSource.subscribers.push(this);
  }

  listenTo() {
    return JobEntity;
  }

  async beforeUpdate(event: UpdateEvent<JobEntity>): Promise<any> {
    // Get data source
    const { entity, databaseEntity, manager, updatedColumns } = event;

    // Prevent invalid entity
    if (!entity) return;

    // TODO: Check transaction if job update failed activity should be not insert to
    // Type guard
    if (entity instanceof JobEntity) {
      // Get job activities repository
      const jobActivitiesRepo = manager.getRepository(JobActivitiesEntity);

      // Make job activities data
      const jobActivity = this.makeDataJobActivity(databaseEntity, entity);

      // Get additional images

      const checkListValues = updatedColumns.find(
        (column) => column.propertyAliasName === 'checkListValues',
      );

      // Check additional images
      if (checkListValues && entity.status === '11_ESTIMATE_PRICE_PROCESSING') {
        const { from, to } = jobActivity.detail.dataChange?.checkListValues ?? {
          from: {},
          to: {},
        };

        const productAdditionalImageChanged = !isEqual(
          from.product_additional_images,
          to.product_additional_images,
        );
        const productImageChanged = !isEqual(
          from.product_images,
          to.product_images,
        );

        let text = 'พนักงานทำการอัปเดต';
        if (productImageChanged) {
          text += 'รายการรูปภาพ';
        }
        if (productAdditionalImageChanged) {
          text += `${productImageChanged ? ' และ' : ''}รูปภาพเพิ่มเติม`;
        }
        const additionalImages = get(
          entity.checkListValues,
          'product_additional_images',
        );

        if (additionalImages) {
          this.makeDataAdditionalImage(
            jobActivity,
            entity.shopUserName ?? entity.shopUserKey,
            text,
          );
        }
      }

      // Prvent comment slug
      if (jobActivity.detail.dataChangeSlug === DataChangeSlug.COMMENTED) {
        return;
      }

      // Save job activities
      await jobActivitiesRepo.save(jobActivity);
    }
  }

  async afterUpdate(event: UpdateEvent<JobEntity>): Promise<void> {
    // Get entity
    const entity = event.entity;

    // Prevent invalid entity
    if (!entity) return;

    // Type check job entity
    if (entity instanceof JobEntity) {
      // Set data firestore with collection path and data
      this.firebaseService.setData(
        makePendingJobPath(entity),
        makeJobData(entity),
      );
    }
  }

  private classifySummary(
    difference: Record<keyof JobEntity, DataChangeInfo>,
  ): Summary | null {
    if (!difference) return null;
    // Get requested at
    const requestedAt = get(difference, 'requestedAt');

    // Get assigned at
    const assignedAt = get(difference, 'assignedAt');

    // Get is additional check list
    const isAdditionalCheckList = get(difference, 'isAdditionalCheckList');

    // Get is status
    const status = get(difference, 'status');

    if (requestedAt) {
      return Summary.JOB_CREATED;
    }

    if (assignedAt) {
      return Summary.JOB_ASSIGNED;
    }

    if (isAdditionalCheckList) {
      return Summary.COMMENTED;
    }

    // Status condition
    if (status) {
      // Indentity requested
      if (status.to === JobStatus.IDENTITY_REQUESTED) {
        return Summary.IDENTITY_REQUESTED;
        // Indentity verified
      } else if (status.to === JobStatus.IDENTITY_VERIFIED) {
        return Summary.IDENTITY_VERIFIED;
        // Indentity rejected
      } else if (status.to === JobStatus.IDENTITY_REJECTED) {
        return Summary.IDENTITY_REJECTED;
      }
    }

    return Summary.JOB_UPDATED;
  }

  private classifyDataChanges(
    difference: Record<keyof JobEntity, DataChangeInfo>,
  ): DataChangeSlug | null {
    if (!difference) return null;

    // Get requested at
    const requestedAt = get(difference, 'requestedAt');

    // Get assigned at
    const assignedAt = get(difference, 'assignedAt');

    // Get is additional check list
    const isAdditionalCheckList = get(difference, 'isAdditionalCheckList');

    // Get is status
    const status = get(difference, 'status');

    if (requestedAt) {
      return DataChangeSlug.JOB_REQUESTED;
    }

    if (assignedAt) {
      return DataChangeSlug.ADMIN_ASSIGNED;
    }

    if (isAdditionalCheckList) {
      return DataChangeSlug.COMMENTED;
    }

    // Status condition
    if (status) {
      // Indentity verified with dipchip
      if (
        status.from === JobStatus.PRICE_ESTIMATED &&
        status.to === JobStatus.IDENTITY_VERIFIED
      ) {
        return DataChangeSlug.IDENTITY_VERIFIED_DIPCHIP;
      }

      // Indentity requested
      if (status.to === JobStatus.IDENTITY_REQUESTED) {
        return DataChangeSlug.IDENTITY_REQUESTED;
        // Indentity verified
      } else if (status.to === JobStatus.IDENTITY_VERIFIED) {
        return DataChangeSlug.IDENTITY_VERIFIED;
        // Indentity rejected
      } else if (status.to === JobStatus.IDENTITY_REJECTED) {
        return DataChangeSlug.IDENTITY_REJECTED;
      }
    }

    return DataChangeSlug.JOB_UPDATED;
  }

  private makeDataJobActivity(previousEntity: JobEntity, entity: JobEntity) {
    // Get difference between entity and database
    const difference = deepDifference<JobEntity>(previousEntity, entity);

    // New job activities entity
    const jobActivity = new JobActivitiesEntity();

    jobActivity.jobId = entity.jobId;
    jobActivity.companyId = entity.companyId;
    jobActivity.type = JobActivitiesType.UPDATE;
    jobActivity.detail = {
      summary: this.classifySummary(difference) ?? Summary.JOB_UPDATED,
      branchId: entity.branchId,
      status: entity.status,
      dataChange: {
        ...JSON.parse(JSON.stringify(difference)),
      },
      shopUserName: entity.shopUserName,
      adminUserName: entity.adminUserName,
      shopUserKey: entity.shopUserKey,
      adminUserKey: entity.adminUserKey,
      dataChangeSlug: this.classifyDataChanges(difference),
      vendorType: entity.vendorType,
    };
    jobActivity.createdBy = entity.updatedBy ?? '';

    return jobActivity;
  }

  private makeDataAdditionalImage(
    jobActivity: JobActivitiesEntity,
    shopUserName: string,
    text: string,
  ) {
    jobActivity.detail.summary = Summary.ADDITIONAL_IMAGE;
    jobActivity.detail.message = text;
    jobActivity.detail.displayName = shopUserName;
    jobActivity.detail.dataChangeSlug = DataChangeSlug.ADDITIONAL_IMAGE;
    jobActivity.type = JobActivitiesType.COMMENT;

    return jobActivity;
  }
}

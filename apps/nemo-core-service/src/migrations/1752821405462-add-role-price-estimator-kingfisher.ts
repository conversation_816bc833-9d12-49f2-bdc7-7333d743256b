import { MigrationInterface, QueryRunner } from "typeorm";
import { RoleEntity, RolePermissionEntity, SiteType } from "../entities";

export class AddRolePriceEstimatorKingfisher1752821405462 implements MigrationInterface {
    name = 'AddRolePriceEstimatorKingfisher1752821405462'

    public async up(queryRunner: QueryRunner): Promise<void> {
       //add role PRICE_ESTIMATOR_KINGFISHER
       await queryRunner.manager.save(RoleEntity, {
               roleId: 'PRICE_ESTIMATOR_KINGFISHER',
               companyId: 'WW',
               roleName: 'PRICE_ESTIMATOR_KINGFISHER',
               type: SiteType.CMS,
           })
        await queryRunner.manager.save(RolePermissionEntity, [
            {
                companyId: 'WW',
                roleId: 'PRICE_ESTIMATOR_KINGFISHER',
                permissionId: 'PS-0004',
                view: true,
                create: false,
                update: true,
                delete: false,
                download: false,
                upload: false,
            },
            {
                companyId: 'WW',
                roleId: 'PRICE_ESTIMATOR_KINGFISHER',
                permissionId: 'PS-0005',
                view: true,
                create: false,
                update: true,
                delete: false,
                download: false,
                upload: false,
            },
        ])
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
       
        //remove role PRICE_ESTIMATOR_KINGFISHER
        const existingRole = await queryRunner.manager.findOne(RoleEntity, {
            where: { companyId: 'WW', roleId: 'PRICE_ESTIMATOR_KINGFISHER' }
        });
        if (existingRole) {
            await queryRunner.manager.delete(RoleEntity, {
                companyId: 'WW',
                roleId: 'PRICE_ESTIMATOR_KINGFISHER'
            });
            await queryRunner.manager.delete(RolePermissionEntity, {
                companyId: 'WW',
                roleId: 'PRICE_ESTIMATOR_KINGFISHER'
            });
        }
    }

}

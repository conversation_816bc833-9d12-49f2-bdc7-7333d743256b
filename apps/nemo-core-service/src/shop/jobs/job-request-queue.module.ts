import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobRequestQueueConsumer } from './job-request-queue.consumer';
import {
  GeneralActivitiesEntity,
  JobEntity,
  UserVendorTypeMappingEntity,
} from '../../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      JobEntity,
      GeneralActivitiesEntity,
      UserVendorTypeMappingEntity,
    ]),
    BullModule.registerQueue({
      name: 'job-request-queue',
      defaultJobOptions: {
        attempts: 1,
        removeOnComplete: true,
        removeOnFail: true,
      },
    }),
  ],
  providers: [BullModule, JobRequestQueueConsumer],
  exports: [BullModule],
})
export class JobRequestQueueModule {}

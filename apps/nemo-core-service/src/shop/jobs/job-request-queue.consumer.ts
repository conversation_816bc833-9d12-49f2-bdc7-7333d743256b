import { Injectable } from '@nestjs/common';
import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  JobEntity,
  JobStatus,
  EmailActivitiesType,
  UserEntity,
  SystemConfigEntity,
  UserVendorTypeMappingEntity,
} from '../../entities';
import { TemplateEmail } from '../../smtp/smtp.service';
import { Permission } from '../../config';
import { emailGeneralTemplate } from '../../../src/utils/email-template';

type TUserCondition = { branchId: string; userType: string };

const requestConfig = {
  [JobStatus.QUOTE_REQUESTED]: {
    userConditions: ({ userType }: TUserCondition) => ({
      userType: userType,
      userRoleBranch: {
        role: {
          rolePermissions: {
            permissionId: Permission.CMS_JOB_ALL,
            update: true,
          },
        },
      },
    }),
    email: (jobId: string, baseUrl: string) => ({
      subject: `มีรายการรอประเมินที่ยังไม่มีผู้รับงาน  Transaction ID ${jobId}`,
      body: [
        'มีรายการรอประเมินที่ยังไม่มีผู้รับงาน',
        `รายการประเมิน  Transaction ID ${jobId} รอรับงานประเมินอยู่`,
      ],
      linkUrl: `${baseUrl}/all-jobs/detail?id=${jobId}`,
      linkText: 'กดเพื่อรับงานประเมิน',
    }),
    site: 'cms',
    slaKey: 'priceEstimatorJob',
    activityType: EmailActivitiesType.QUOTE_REQUESTED,
  },
  [JobStatus.IDENTITY_REQUESTED]: {
    userConditions: ({ branchId }: TUserCondition) => ({
      userRoleBranch: {
        role: {
          rolePermissions: {
            permissionId: Permission.SHOP_OTHERS_OCR_CONFIRM,
            update: true,
          },
        },
        branchId,
      },
    }),
    email: (jobId: string, baseUrl: string) => ({
      subject: `มีรายการรออนุมัติยืนยันตัวตน  Transaction ID ${jobId}`,
      body: [
        'มีรายการรออนุมัติยืนยันตัวตน',
        `รายการรออนุมัติยืนยันตัวตน  Transaction ID ${jobId}`,
      ],
      linkUrl: `${baseUrl}/jobs/detail?id=${jobId}&team-jobs=true&view=process`,
      linkText: 'กดเพื่ออนุมัติ',
    }),
    site: 'frontshop',
    slaKey: 'managerApprove',
    activityType: EmailActivitiesType.IDENTITY_REQUESTED,
  },
};

@Processor('job-request-queue')
@Injectable()
class JobRequestQueueConsumer {
  constructor(
    @InjectRepository(JobEntity)
    private readonly JobRepo: Repository<JobEntity>,
    @InjectRepository(UserEntity)
    private readonly UserRepo: Repository<UserEntity>,
    @InjectRepository(SystemConfigEntity)
    private readonly systemConfigRepo: Repository<SystemConfigEntity>,
    @InjectRepository(UserVendorTypeMappingEntity)
    private readonly userVendorTypeMappingRepo: Repository<UserVendorTypeMappingEntity>,
    @InjectQueue('email-queue')
    private readonly emailQueue: Queue,
  ) {}
  @Process('job-request')
  async jobRequest(
    job: Job<{ companyId: string; jobId: string; type?: string }>,
  ) {
    const { companyId, jobId } = job.data;
    const type = job.data.type ?? JobStatus.QUOTE_REQUESTED;
    const config = requestConfig[type];

    if (!config) {
      return;
    }

    const targetJob = await this.JobRepo.findOne({
      where: { jobId, companyId, status: type as JobStatus },
    });

    const { branchId, vendorType } = targetJob || {};

    const userVendorMapping = await this.userVendorTypeMappingRepo.findOne({
      where: {
        vendorType,
      },
    });

    const { userType } = userVendorMapping || { userType: '' };

    const userConditionProp = {
      branchId: branchId ?? '',
      userType,
    };

    const targetUsers = await this.UserRepo.find({
      where: {
        companyId,
        ...config.userConditions(userConditionProp),
      },
    });

    const receiver: string[] = targetUsers
      .map((user) => user.email?.trim() ?? '')
      .filter((email) => email !== '');

    const configBaseUrlData = await this.systemConfigRepo.findOne({
      where: { configKey: 'base_url', companyId },
    });
    const configSlaData = await this.systemConfigRepo.findOne({
      where: { configKey: 'sla_time', companyId },
    });

    const baseUrl = configBaseUrlData?.data[config.site] ?? '';
    const slaTime = configSlaData?.data[config.slaKey] ?? 0;

    if (targetJob && receiver.length > 0 && baseUrl) {
      const emailConfig = config.email(jobId, baseUrl);
      const sender = process.env.EMAIL_SENDER ?? '<EMAIL>';
      const linkUrl = emailConfig.linkUrl;

      const templateEmail: TemplateEmail = {
        mailOption: {
          from: sender,
          to: receiver,
          subject: emailConfig.subject,
          html: emailGeneralTemplate({
            bodyLine: emailConfig.body,
            linkUrl,
            linkText: emailConfig.linkText,
          }),
        },
        emailActivity: {
          refId: jobId,
          companyId: companyId,
          type: config.activityType,
          detail: { sendEmailInfo: { from: sender, to: receiver } },
        },
      };

      await this.emailQueue.add('send-email', templateEmail);
      if (slaTime) {
        await job.queue.add(
          'job-request',
          { companyId, jobId, type: type },
          { delay: slaTime },
        );
      }
    }
  }
}
export { JobRequestQueueConsumer };

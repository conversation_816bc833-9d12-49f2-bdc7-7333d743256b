import { Injectable } from '@nestjs/common';
import {
  ConfirmContractDto,
  CreateFromDraftDto,
  Create<PERSON>obDraftDto,
  CreateJobDto,
  IdentityRejectedDto,
  SignContractDto,
  UpdateCustomerInfoJobDto,
  PostOcrDto,
} from './dto';
import { WithBranchContext, WithUserContext } from '../../interfaces';
import {
  JobActivityDetail,
  JobActivitiesEntity,
  JobActivitiesType,
  JobEntity,
  JobStatus,
  JobTemplateEntity,
  ModelMasterEntity,
  PenaltiesView,
  CustomerInfo,
  CustomerInfoType,
  BranchEntity,
  CompanyEntity,
  ContractEntity,
  SystemConfigEntity,
  EstimationActivitiesEntity,
  ChecklistType,
  QuestionType,
  GeneralActivitiesEntity,
  AOShippingStatus,
  CampaignEntity,
  CampaignRedemptionCodeEntity,
} from '../../entities';
import { MasterAddressEntity } from '../../entities/address-master.entity';
import { InjectRepository } from '@nestjs/typeorm';
import {
  EntityManager,
  Not,
  Repository,
  SelectQueryBuilder,
  In,
  LessThanOrEqual,
  MoreThanOrEqual,
} from 'typeorm';
import { S3Service } from '../../storage/s3.service';
import { ImageJobParamDto } from './dto/image-param.dto';
import { Request } from 'express';
import { assign, omit } from 'lodash';
import { BaseExceptionService } from '../../exceptions';
import { ContractsService } from '../contracts/contracts.service';
import { SignContractResponse } from 'contracts';
import { dynamicImport } from '../../utils/dynamic-import';
import { CacheManagerService } from '../../cache-manager/cache-manager.service';
import { RedisUtils } from '../../utils/redis.util';
import { DateTime } from 'luxon';
import { SignDepositContractDto } from './dto/sign-deposit-contract.dto';
import { CreateJobDraftSignDto } from './dto/create-job-draft-sign.dto';
import { getFunctionFromModelMasterFn } from '../../utils/common-order';
import { OcrService } from '../../ocr/ocr.service';
import { convertBase64ToBytes } from '../../utils/contract/PDF-config-general/helper';
import {
  BASE_EXCEPTIONS,
  getS3JobUrlPath,
  Permission,
  PermissionAction,
  productImageConfig,
} from '../../../src/config';
import { generateProductImageSlugForTemplate } from '../../../src/utils/survey-template/dynamic-product-images';
import { getJobWithRelation, IJobWithRelationType } from './service/general';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

const IMAGE_FILE_TYPE = ['image/jpeg', 'image/png', 'image/jpg'];
const QR_EXPIRE_HOURS = 6; // change to config in DB

@Injectable()
export class JobsService {
  constructor(
    @InjectRepository(ModelMasterEntity)
    private readonly modelMastersRepo: Repository<ModelMasterEntity>,
    @InjectRepository(JobEntity)
    private readonly jobsRepo: Repository<JobEntity>,
    @InjectRepository(ContractEntity)
    private readonly contractsRepo: Repository<ContractEntity>,
    @InjectRepository(JobTemplateEntity)
    private readonly jobTemplatesRepo: Repository<JobTemplateEntity>,
    @InjectRepository(PenaltiesView)
    private readonly penaltiesViewRepo: Repository<PenaltiesView>,
    @InjectRepository(JobActivitiesEntity)
    private readonly jobActivitiesRepo: Repository<JobActivitiesEntity>,
    @InjectRepository(BranchEntity)
    private readonly branchRepo: Repository<BranchEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepo: Repository<CompanyEntity>,
    private readonly s3Service: S3Service,
    private readonly baseExceptionService: BaseExceptionService,
    private readonly contractsService: ContractsService,
    private readonly cacheManager: CacheManagerService,
    @InjectRepository(SystemConfigEntity)
    private readonly systemConfigRepo: Repository<SystemConfigEntity>,
    @InjectRepository(EstimationActivitiesEntity)
    private readonly estimationActivitiesRepo: Repository<EstimationActivitiesEntity>,
    private readonly ocrService: OcrService,
    @InjectRepository(MasterAddressEntity)
    private readonly masterAddressRepository: Repository<MasterAddressEntity>,
    @InjectRepository(GeneralActivitiesEntity)
    private readonly generalActivitiesRepository: Repository<GeneralActivitiesEntity>,
    @InjectRepository(CampaignEntity)
    private readonly campaignRepo: Repository<CampaignEntity>,
    @InjectRepository(CampaignRedemptionCodeEntity)
    private readonly campaignRedemptionCodeRepo: Repository<CampaignRedemptionCodeEntity>,
    @InjectQueue('job-request-queue')
    private readonly jobRequestQueue: Queue,
  ) {}

  buildSearchQuery(
    context: Request,
    listQuery: SelectQueryBuilder<JobEntity>,
  ): SelectQueryBuilder<JobEntity> {
    const isExactSearch = context.query.isExactSearch;
    const search = context.query.search as string;
    const deviceKey = context.query.deviceKey as string;
    const status = context.query.status as string[];
    const excludeStatus = context.query.excludeStatus as string[];
    const shopUserKey = context.query.shopUserKey as string;
    const branch = context.query.branch as string;
    const isAvailableForDo = context.query.isAvailableForDo as string;
    const allocationOrderId = context.query.allocationOrderId as string;
    const isExcludeUser = context.query.isExcludeUser as string;
    const aoShippingStatus = context.query.aoShippingStatus as string;

    // Search functionality
    if (search) {
      const searchableFields: (keyof JobEntity)[] = ['jobId', 'deviceKey'];

      if (!isExcludeUser) {
        searchableFields.push('shopUserName');
      }

      const searchConditions = searchableFields.map((field) =>
        isExactSearch ? `r.${field} = :${field}` : `r.${field} ILIKE :${field}`,
      );

      const searchParams = searchableFields.reduce(
        (acc, field) => ({
          ...acc,
          [field]: isExactSearch ? search : `%${search}%`,
        }),
        {},
      );

      listQuery.andWhere(`(${searchConditions.join(' OR ')})`, searchParams);
    }

    // Construct filter conditions & apply conditions
    const conditions = [
      deviceKey && `r.deviceKey ILIKE '%${deviceKey}%'`,
      status && `r.status IN ('${status.map((s) => s).join("','")}')`,
      excludeStatus &&
        `r.status NOT IN ('${excludeStatus.map((s) => s).join("','")}')`,
      shopUserKey && `r.shopUserKey = '${shopUserKey}'`,
      branch && `r.branchId = '${branch}'`,
      allocationOrderId && `r.allocationOrderId = '${allocationOrderId}'`,
      aoShippingStatus && `r.aoShippingStatus = '${aoShippingStatus}'`,
    ].filter(Boolean);

    // Check is available for do condition
    if (isAvailableForDo) {
      if (isAvailableForDo === 'new') {
        listQuery.andWhere('r.deliveryOrderId IS NULL');
      } else {
        listQuery.andWhere(
          '(r.deliveryOrderId IS NULL OR r.deliveryOrderId = :deliveryOrderId)',
          {
            deliveryOrderId: isAvailableForDo,
          },
        );
      }
    }

    if (conditions.length) listQuery.andWhere(conditions.join(' AND '));

    return listQuery;
  }

  sanitizeInputBody(
    data: Partial<JobEntity>,
    isCreated: boolean = false,
  ): Partial<JobEntity> {
    // unique key arrays, omit before process an update action
    return isCreated ? data : omit(data, ['jobId']);
  }

  preSave(
    man: EntityManager,
    data: Partial<JobEntity>,
    isCreated: boolean,
  ): Partial<JobEntity> {
    // unique key arrays, omit before process an update action
    return isCreated ? data : man.create(JobEntity, omit(data, ['color']));
  }

  computeUpdatePayload(current: JobEntity, data: Partial<JobEntity>) {
    const allowed = this.getAllowedStatus(current, data);

    // Check if the current status allows the update, throw exception if not
    if (!allowed.includes(current.status)) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Invalid input for update job - The status must be: ${
          allowed.join(' or ') || Object.values(JobStatus).join(' or ')
        }. Current status is: ${current.status}`,
      );
    }

    return data;
  }

  async getJobWithRelation({
    jobId,
    relations,
    type,
    company,
  }: {
    jobId: string;
    relations: string[];
    type?: IJobWithRelationType;
    company: string;
  }) {
    return await getJobWithRelation({
      jobId,
      type,
      relations,
      jobRepo: this.jobsRepo,
      company,
    });
  }

  getAllowedStatus(current: JobEntity, data: Partial<JobEntity>): string[] {
    if (data.status) {
      // Specific cases for allowed status transitions
      switch (data.status) {
        case JobStatus.QUOTE_REQUESTED:
          return [JobStatus.DRAFT];
        case JobStatus.ESTIMATE_PRICE_PROCESSING:
          return [JobStatus.QUOTE_REQUESTED];
        case JobStatus.PRICE_ESTIMATED:
          return [JobStatus.ESTIMATE_PRICE_PROCESSING];
        case JobStatus.IDENTITY_REQUESTED:
          return [JobStatus.PRICE_ESTIMATED, JobStatus.IDENTITY_REJECTED];
        case JobStatus.IDENTITY_REJECTED:
          return [JobStatus.IDENTITY_REQUESTED];
        case JobStatus.IDENTITY_VERIFIED:
          if (current.contract) {
            const customerInfo = current.contract.customerInfo;
            if (customerInfo?.type === CustomerInfoType.IDENTITY_VERIFICATION) {
              return [JobStatus.IDENTITY_REQUESTED];
            }
          }
          return [JobStatus.PRICE_ESTIMATED];
        case JobStatus.PURCHASED:
          data.purchasedPrice = current.suggestedPrice;
          return [JobStatus.CAMPAIGN_SELECTED];
        case JobStatus.REJECT_BY_SHOP:
        case JobStatus.REJECT_BY_CUSTOMER:
          return [
            JobStatus.QUOTE_REQUESTED,
            JobStatus.ESTIMATE_PRICE_PROCESSING,
            JobStatus.PRICE_ESTIMATED,
            JobStatus.IDENTITY_REJECTED,
            JobStatus.IDENTITY_VERIFIED,
            JobStatus.CAMPAIGN_SELECTED,
          ];
        default:
          return [];
      }
    }
    // If status unchanged, return allowed status for update job data
    if (data.isAdditionalCheckList || data.checkListValues) {
      return [JobStatus.QUOTE_REQUESTED, JobStatus.ESTIMATE_PRICE_PROCESSING];
    }
    return [];
  }

  transformOrderBy(orderBy?: string): string | undefined {
    if (orderBy) {
      // If orderBy includes brand, model or rom, replace it with modelIdentifiers
      const field = ['brand', 'model', 'rom', 'awbNumber'].find((s) =>
        orderBy.includes(s),
      );

      if (field) {
        if (orderBy.includes('awbNumber')) {
          return orderBy.replace(field, `rel:deliveryOrder awbNumber`);
        }

        return orderBy.replace(field, `modelIdentifiers ${field}`);
      }
    }

    return orderBy;
  }

  checkJobPermission(
    job: JobEntity,
    user: WithUserContext,
    branch: WithBranchContext,
  ): void {
    // Check if the user is the shop owner or manager
    //const isManager = checkIsManager(branch.branch, user.roles ?? []);
    // Check user can access the job
    //const invalidJobOwner = !isManager && job.shopUserKey !== user.userKey;
    const invalidBranch = job.branchId !== branch.branch;

    if (invalidBranch) {
      throw this.baseExceptionService.exception(
        'INVALID_JOB_PERMISSION',
        'User does not have permission to access this job',
      );
    }
  }

  async prepareJobDraftSign(
    body: CreateJobDraftSignDto,
    user: WithUserContext,
    branch: WithBranchContext,
  ): Promise<JobEntity> {
    const { estimationId, contractKey } = body;
    const { company: companyId, userKey, name } = user;

    const targetEA = await this.estimationActivitiesRepo.findOne({
      where: { id: estimationId, companyId },
    });

    if (!targetEA) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA');
    }

    const {
      modelKey,
      imei1,
      imei2,
      colorId,
      modelChecklistResult,
      createdAt,
      jobId,
    } = targetEA;

    if (jobId) {
      throw this.baseExceptionService.exception(
        'UNAVAILABLE_ESTIMATION_ACTIVITY',
      );
    }

    const minTime = DateTime.now().minus({ hours: QR_EXPIRE_HOURS });
    const createTime = DateTime.fromJSDate(createdAt);

    if (createTime < minTime) {
      throw this.baseExceptionService.exception('EXPIRED_ESTIMATION_ACTIVITY');
    }

    const targetModel = await this.modelMastersRepo.findOne({
      where: { modelKey, companyId },
      relations: {
        modelMasterColors: true,
        modelMasterFunction: { modelChecklist: true },
      },
    });

    if (!targetModel) {
      throw this.baseExceptionService.exception(
        'INVALID_MODEL_IDENTIFIERS',
        'Target Model not found',
      );
    }

    const { modelMasterFunction, modelIdentifiers, templateId } = targetModel;
    if (!modelMasterFunction || !templateId) {
      throw this.baseExceptionService.exception(
        'INVALID_MODEL_IDENTIFIERS',
        'ModelMasterFunction or templateId not found',
      );
    }

    const targetDefaultJobTemplate = await this.jobTemplatesRepo.findOne({
      where: { templateId, companyId },
    });

    if (!targetDefaultJobTemplate) {
      throw this.baseExceptionService.exception(
        'INVALID_MODEL_IDENTIFIERS',
        'Not found defaultJobTemplate',
      );
    }

    const penalties = await this.penaltiesViewRepo.findOne({
      where: { modelKey, companyId },
    });
    // --- Const --- //
    let moduleCount = 0;
    const elementModules: {
      name: string;
      type: 'module';
      title: {
        th: string;
        en: string;
      };
      description: {
        th: string;
        en: string;
      };
      iconImageUrl: string;
      isRequired: boolean;
    }[] = [];
    const moduleChecklistValue: { [k: string]: string } = {};

    const elementQuestions: {
      name: string;
      title: {
        th: string;
        en: string;
      };
      description: {
        th: string;
        en: string;
      };
      tooltip: {
        th: string;
        en: string;
      };
      placeholder: {
        th: string;
        en: string;
      };
      requiredErrorText: {
        th: string;
        en: string;
      };
      isRequired: boolean;
      isIncludeVideo: boolean;
      type: 'question_option' | 'question_selection' | 'question_text';
      colCount?: number; //omit when type text
      choices?: [
        {
          text: {
            th: string;
            en: string;
          };
          value: string;
          status?: 'positive' | 'negative'; //only selection type
        },
      ]; //omit when type text
    }[] = [];
    const questionChecklistValue: { [k: string]: string | string[] } = {};

    const {
      moduleMasterFunctionByFunction,
      questionMasterFunctionByFunction,
      allModuleChecklistIds,
      allQuestionChecklistIds,
      requireModuleChecklistIds,
      requireQuestionChecklistIds,
      moduleMasterFunctionPointer,
      questionMasterFunctionPointer,
    } = getFunctionFromModelMasterFn(modelMasterFunction);

    const {
      [ChecklistType.MODULE]: moduleResult,
      [ChecklistType.QUESTION]: questionResult,
    } = modelChecklistResult;

    const modules = moduleResult.reduce((acc, current) => {
      acc[current.modelChecklistId] = current;
      return acc;
    }, {});

    const questions = questionResult.reduce((acc, current) => {
      acc[current.modelChecklistId] = current;
      return acc;
    }, {});

    // --- template element generate --- //
    allModuleChecklistIds.forEach((id) => {
      let isSkip: boolean = true;
      let keyCondValue: string = 'skip';
      if (modules[id]) {
        const { isSkip: skip, functionKeyCond } = modules[id];
        keyCondValue = functionKeyCond.split('=')[1];
        isSkip = skip;
      }
      const { fnName } = moduleMasterFunctionPointer[id];

      const moduleFunction =
        moduleMasterFunctionByFunction[fnName][keyCondValue];

      const {
        modelChecklistNameTh,
        modelChecklistNameEn,
        modelCheckListDescriptionTh,
        modelCheckListDescriptionEn,
        functionKey,
        iconImageUrl,
        isRequired,
      } = moduleFunction;
      if (!isSkip) {
        requireModuleChecklistIds.delete(id);
      }
      elementModules.push({
        name: functionKey,
        type: 'module',
        title: {
          th: modelChecklistNameTh,
          en: modelChecklistNameEn,
        },
        description: {
          th: modelCheckListDescriptionTh,
          en: modelCheckListDescriptionEn,
        },
        iconImageUrl: iconImageUrl,
        isRequired: isRequired,
      });
      moduleCount += 1;

      moduleChecklistValue[functionKey] = keyCondValue;
    });

    allQuestionChecklistIds.forEach((id) => {
      let isSkip: boolean = true;
      let keyCondValue: string = 'skip';
      let answerChoices: any[] = [];
      if (questions[id]) {
        const {
          isSkip: skip,
          functionKeyCond,
          questionChoices,
        } = questions[id];
        keyCondValue = functionKeyCond.split('=')[1];
        isSkip = skip;
        answerChoices = questionChoices;
      }
      const { fnName } = questionMasterFunctionPointer[id];
      const questionFunctionAll = questionMasterFunctionByFunction[fnName];
      const questionFunction = questionFunctionAll[keyCondValue];

      const {
        modelChecklistNameTh,
        modelChecklistNameEn,
        modelCheckListDescriptionTh,
        modelCheckListDescriptionEn,
        tooltipEn,
        tooltipTh,
        placeholderEn,
        placeholderTh,
        errorTextEn,
        errorTextTh,
        functionKey,
        isRequired,
        answerType,
        questionChoices,
        isIncludeVideo,
        popup,
        tooltip,
      } = questionFunction;
      if (!isSkip) {
        requireQuestionChecklistIds.delete(id);
      }

      const element: any = {
        name: functionKey,
        title: {
          th: modelChecklistNameTh,
          en: modelChecklistNameEn,
        },
        description: {
          th: modelCheckListDescriptionTh,
          en: modelCheckListDescriptionEn,
        },
        tooltip: {
          th: tooltipTh,
          en: tooltipEn,
        },
        placeholder: {
          th: placeholderTh,
          en: placeholderEn,
        },
        requiredErrorText: {
          th: errorTextTh,
          en: errorTextEn,
        },
        isRequired,
        isIncludeVideo,
        popup,
        tooltipConfig: tooltip,
      };

      const answers =
        answerChoices
          ?.filter((choice) => choice.isSelected === true)
          .map((choice) => choice.id) || [];

      if (answerType === QuestionType.OPTION) {
        element.type = 'question_option';
        element.colCount = 4;
        element.choices = questionChoices.map((choice) => ({
          text: { th: choice.answerTh, en: choice.answerEn },
          value: choice.id,
        }));
        questionChecklistValue[functionKey] = isSkip
          ? 'USER_SKIP_ANSWER'
          : answers;
      } else if (answerType === QuestionType.SELECTION) {
        element.type = 'question_selection';
        element.colCount = 4;
        element.choices = questionChoices.map((choice) => {
          const { penalties } = questionFunctionAll[choice.id];
          return {
            text: { th: choice.answerTh, en: choice.answerEn },
            value: choice.id,
            status: Number(penalties) < 0 ? 'negative' : 'positive',
          };
        });
        questionChecklistValue[functionKey] = answers[0] || 'USER_SKIP_ANSWER';
      } else if (answerType === QuestionType.TEXT) {
        element.type = 'question_text';
      }

      elementQuestions.push(element);
    });

    // --- template create --- //
    const template = targetDefaultJobTemplate.template.map((item: any) => {
      if (item.slug === 'remobie_check_list') {
        item.fillSpaceColumnCount = 4;
        item.title = `ผลการตรวจสอบฟังก์ชันการทำงาน (${moduleCount} รายการ)`;
        item.survey_form.pages[0].elements = elementModules;
        return item;
      }
      if (item.slug === 'product_information') {
        item.survey_form.pages[0].elements = elementQuestions;
        item.fillSpaceColumnCount = 1;
      }
      if (item.slug === 'product_images') {
        item.survey_form.pages[0].elements =
          generateProductImageSlugForTemplate({
            config: productImageConfig,
            itemPerRow: 4,
          });
      }

      return item;
    });

    const checkListValues = {
      remobie_check_list: moduleChecklistValue,
      product_information: questionChecklistValue,
    };

    // --- Entity --- //
    const branchId = branch.branch;
    const newJobId = await this.generateJobId(branchId);

    const depositContractLink =
      await this.contractsService.uploadDepositContract(
        newJobId,
        companyId,
        contractKey,
      );

    delete targetModel.modelMasterColors;
    delete targetModel.modelMasterFunction;

    // --- validate --- //
    const requireChecklistRemain = Array.from(
      new Set([...requireModuleChecklistIds, ...requireQuestionChecklistIds]),
    );

    if (requireChecklistRemain.length) {
      throw this.baseExceptionService.exception('INVALID_ESTIMATION_ACTIVITY');
    }

    const jobEntity = this.defaultPrepareJob({
      jobId: newJobId,
      companyId,
      branchId,
      deviceKey: imei1,
      modelKey: modelKey,
      createdBy: userKey,
      updatedBy: userKey,
      shopUserKey: userKey,
      shopUserName: name,
      status: JobStatus.DRAFT,
      modelIdentifiers: modelIdentifiers,
      modelTemplate: targetModel,
      penalties: penalties?.penalties ?? null,
      checkList: template,
      checkListValues,
      depositContractLink,
      colorId,
      draftEstimationId: estimationId,
    });
    if (imei2) {
      jobEntity.deviceKey2 = imei2;
    }

    // Create query runner
    const queryRunner = this.jobsRepo.manager.connection.createQueryRunner();

    // Connect
    await queryRunner.connect();

    // Start transaction
    await queryRunner.startTransaction();
    try {
      // Insert job
      const insertResult = await queryRunner.manager
        .getRepository(JobEntity)
        .createQueryBuilder()
        .insert()
        .values(jobEntity)
        .orIgnore()
        .execute();

      // If insert failed conflict, fallback to generate a job id
      if (!insertResult.raw.length) {
        const { key } = this.generateJobIdKey(branchId);

        const { jobId, jobCount } = await this.fallBackGenerateJobId(branchId);

        jobEntity.jobId = jobId;

        await queryRunner.manager.save(JobEntity, jobEntity);

        await this.cacheManager.setData(key, jobCount, RedisUtils.dayTTL * 31);
      }

      // Commit transaction
      await queryRunner.commitTransaction();
    } catch (err) {
      // Rollback
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      // Release
      await queryRunner.release();
    }

    return jobEntity;
  }

  async prepareJobDraft(
    body: CreateJobDraftDto,
    user: WithUserContext,
    branch: WithBranchContext,
  ): Promise<JobEntity> {
    const { modelKey, deviceKey, depositContractKey } = body;
    const { company, userKey, name } = user;

    const modelMaster = await this.modelMastersRepo.findOne({
      where: { modelKey, companyId: company },
    });

    if (!modelMaster) {
      throw this.baseExceptionService.exception('INVALID_MODEL_IDENTIFIERS');
    }

    // Find penalties by modelKey
    const penalties = await this.penaltiesViewRepo.findOne({
      where: { modelKey, companyId: company },
    });

    // Find job template by templateId
    const jobTemplate = await this.jobTemplatesRepo.findOne({
      where: { templateId: modelMaster.templateId, companyId: company },
    });

    // Create and populate the JobEntity
    const newJobId = await this.generateJobId(branch.branch);

    const depositContractLink =
      await this.contractsService.uploadDepositContract(
        newJobId,
        company,
        depositContractKey,
      );

    const jobEntity = this.defaultPrepareJob({
      jobId: newJobId,
      companyId: company,
      deviceKey: deviceKey,
      branchId: branch.branch,
      modelKey: modelKey,
      createdBy: userKey,
      updatedBy: userKey,
      shopUserKey: userKey,
      shopUserName: name,
      status: JobStatus.DRAFT,
      modelIdentifiers: modelMaster.modelIdentifiers,
      modelTemplate: modelMaster,
      penalties: penalties?.penalties ?? null,
      checkList: jobTemplate?.template ?? [],
      checkListValues: {} as any,
      depositContractLink,
    });

    // Create query runner
    const queryRunner = this.jobsRepo.manager.connection.createQueryRunner();

    // Connect
    await queryRunner.connect();

    // Start transaction
    await queryRunner.startTransaction();
    try {
      // Insert job
      const insertResult = await queryRunner.manager
        .getRepository(JobEntity)
        .createQueryBuilder()
        .insert()
        .values(jobEntity)
        .orIgnore()
        .execute();

      const branchId = branch.branch;
      // If insert failed conflict, fallback to generate a job id
      if (!insertResult.raw.length) {
        const { key } = this.generateJobIdKey(branchId);

        const { jobId, jobCount } = await this.fallBackGenerateJobId(branchId);

        jobEntity.jobId = jobId;

        await queryRunner.manager.save(JobEntity, jobEntity);

        await this.cacheManager.setData(key, jobCount, RedisUtils.dayTTL * 31);
      }

      // Commit transaction
      await queryRunner.commitTransaction();
    } catch (err) {
      // Rollback
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      // Release
      await queryRunner.release();
    }

    return jobEntity;
  }

  async fallBackGenerateJobId(branchId: string): Promise<{
    jobId: string;
    jobCount: number;
  }> {
    const { dateCode } = this.generateJobIdKey(branchId);
    const shortBranchId = this.getBranchIdShort(branchId);

    // Get latest jobId
    const latestJobIdInMonth = await this.jobsRepo
      .createQueryBuilder('job')
      .select(['job.jobId', 'job.createdAt'])
      .where('job.jobId LIKE :latestId', {
        latestId: `%${DateTime.now().toFormat('yyMM')}-${shortBranchId}%`,
      })
      .orderBy('job.jobId', 'DESC')
      .getOne();

    if (!latestJobIdInMonth) {
      return {
        jobId: `${dateCode}-${shortBranchId}-0001`,
        jobCount: 1,
      };
    }

    // Get latest job prefix
    const jobIdCountFromDb =
      parseInt(latestJobIdInMonth.jobId.slice(-4).replace(/^0*/g, '') || '0') +
      1;

    // Format jobId with fallback from db
    const jobId = `${dateCode}-${shortBranchId}-${String(
      jobIdCountFromDb,
    ).padStart(4, '0')}`;

    // Return jobId
    return {
      jobId,
      jobCount: jobIdCountFromDb,
    };
  }

  getBranchIdShort(branchId: string) {
    return branchId.padStart(10, '0');
  }
  async generateJobId(branch: string): Promise<string> {
    // get first digit and last 3 digits of branch id
    const branchId = this.getBranchIdShort(branch);

    const { key, dateCode } = this.generateJobIdKey(branchId);

    // Increment do count
    const jobCount = await this.cacheManager.incrData(
      key,
      RedisUtils.dayTTL * 31,
    );

    const jobIdNumber = `${dateCode}-${branchId}-${String(jobCount).padStart(
      4,
      '0',
    )}`;

    return jobIdNumber;
  }

  generateJobIdKey(branchId: string): { key: string; dateCode: string } {
    // Get the current date
    const currentDate = DateTime.now();

    // Get the current year/month yyMM
    const formatCurrentDate = currentDate.toFormat('yyMM');

    // Increment and pad the job count for the current month
    return {
      key: `doIndexInMonth_${formatCurrentDate}${branchId}`,
      dateCode: formatCurrentDate,
    };
  }

  prepareJobQuoteRequest(body: CreateJobDto, user: WithUserContext): JobEntity {
    // Create the JobEntity
    const jobEntity = this.defaultPrepareJob({
      updatedBy: user.userKey,
      status: JobStatus.QUOTE_REQUESTED,
      deviceKey2: body.deviceKey2,
      checkListValues: body.checkListValues,
      requestedAt: new Date(),
    });

    return jobEntity;
  }

  prepareAssignJob(user: WithUserContext): JobEntity {
    const { userKey, name } = user;
    // Create the JobEntity
    const jobEntity = this.defaultPrepareJob({
      updatedBy: userKey,
      adminUserKey: userKey,
      adminUserName: name,
      status: JobStatus.ESTIMATE_PRICE_PROCESSING,
      assignedAt: new Date(),
    });

    return jobEntity;
  }

  prepareRejectJob(
    user: WithUserContext,
    status: JobStatus.REJECT_BY_SHOP | JobStatus.REJECT_BY_CUSTOMER,
  ): JobEntity {
    const now = new Date();
    // Create the JobEntity
    const jobEntity = this.defaultPrepareJob({
      updatedBy: user.userKey,
      status,
      rejectedAt: now,
      completedShopAt: now,
      campaigns: [],
    });

    return jobEntity;
  }

  preparePurchasedJob(user: WithUserContext): JobEntity {
    console.log('preparePurchasedJob');
    const now = new Date();
    // Create the JobEntity
    const jobEntity = this.defaultPrepareJob({
      purchasedAt: now,
      completedShopAt: now,
      updatedBy: user.userKey,
      status: JobStatus.PURCHASED,
    });

    return jobEntity;
  }

  prepareVerifiedJob(
    user: WithUserContext,
    type: CustomerInfoType,
    contract?: ContractEntity | null,
    currentStatus?: string,
  ): JobEntity {
    if (contract) {
      // Current customer info type must be the same as the type to be approved
      if (contract.customerInfo.type !== type) {
        throw this.baseExceptionService.exception(
          'INVALID_INPUT_FOR_UPDATE_JOB',
          `Invalid customer info type - Requires ${type} type`,
        );
      }
    } else {
      // IDENTITY_VERIFICATION type is only allowed for job with contract
      if (type === CustomerInfoType.IDENTITY_VERIFICATION) {
        throw this.baseExceptionService.exception(
          'INVALID_INPUT_FOR_UPDATE_JOB',
          `Invalid input for update job - The status must be: ${JobStatus.IDENTITY_REQUESTED}. Current status is: ${currentStatus}`,
        );
      }
    }

    // Create the JobEntity
    const jobEntity = this.defaultPrepareJob({
      updatedBy: user.userKey,
      status: JobStatus.IDENTITY_VERIFIED,
    });

    return jobEntity;
  }

  defaultPrepareJob(body: Partial<JobEntity>): JobEntity {
    // Merge fields from body to jobEntity
    const jobEntity = new JobEntity();
    Object.assign(jobEntity, body);

    return jobEntity;
  }

  async getDraftContract(job: JobEntity): Promise<string> {
    const { status, branchId, companyId } = job;
    if (status !== JobStatus.CAMPAIGN_SELECTED)
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Invalid input for get draft contract - The status must be: ${JobStatus.CAMPAIGN_SELECTED}. Current status is: ${job.status}`,
      );
    const branch = await this.branchRepo.findOne({
      where: { branchId, companyId },
    });

    const company = await this.companyRepo.findOne({
      where: { companyId },
    });

    const branchTitle = `${branch?.branchId} - ${branch?.title}`.trim();

    const contract = await this.contractsService.getDraftContract(
      job,
      branchTitle,
      company?.logoPath ?? '',
      company?.contractEmail ?? '',
    );

    return contract;
  }

  async getSignContract(
    signContract: SignContractDto,
    job: JobEntity,
  ): Promise<SignContractResponse> {
    if (job.status !== JobStatus.CAMPAIGN_SELECTED)
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Invalid input for sign contract - The status must be: ${JobStatus.IDENTITY_VERIFIED}. Current status is: ${job.status}`,
      );

    const branch = await this.branchRepo.findOne({
      where: { branchId: job.branchId, companyId: job.companyId },
    });

    const company = await this.companyRepo.findOne({
      where: { companyId: job.companyId },
    });

    const branchTitle = `${branch?.branchId} - ${branch?.title}`.trim();

    const contract = await this.contractsService.getDraftContract(
      job,
      branchTitle,
      company?.logoPath ?? '',
      company?.contractEmail ?? '',
    );

    const contractSigned = await this.contractsService.getSignContract(
      contract,
      signContract.signature,
    );

    return contractSigned;
  }

  async getSignDepositContract({
    company,
    body,
  }: {
    company: string;
    body: SignDepositContractDto;
  }): Promise<SignContractResponse> {
    const { signature } = body;

    const contract =
      (
        await this.systemConfigRepo.findOne({
          where: { configKey: 'default_deposit_contract', companyId: company },
        })
      )?.data || '';

    const contractSigned = await this.contractsService.getSignDepositContract(
      contract,
      signature,
    );

    return contractSigned;
  }

  async sendEmailContract({
    jobId,
    user,
  }: {
    jobId: string;
    user: WithUserContext;
  }): Promise<void> {
    if (!jobId || !user) {
      throw this.baseExceptionService.exception('BODY_PAYLOAD_INVALID');
    }

    const { company: companyId } = user;

    const job = await this.jobsRepo.findOne({
      where: { jobId, companyId },
      relations: { campaignRedemptionCode: true },
    });

    const contract = await this.contractsRepo.findOne({
      where: { jobId },
      relations: { importedVouchers: true },
    });

    if (!job || !contract) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        `Not found job or contract`,
      );
    }

    const isReSend = true;
    await this.contractsService.sendContractToEmail(job, contract, isReSend);
  }

  async getImageJob(user: WithUserContext, imageJobParamDto: ImageJobParamDto) {
    // Generate the S3 key path
    const keyPath = `company/${user.company}/jobs/${imageJobParamDto.id}/${imageJobParamDto.slug}/${imageJobParamDto.key}`;

    return this.s3Service.getPreviewUrl(keyPath);
  }

  async uploadImageJob(
    fileStream: Buffer,
    user: WithUserContext,
    imageJobParamDto: ImageJobParamDto,
  ) {
    // Generate the S3 key path
    const keyPath = `company/${user.company}/jobs/${imageJobParamDto.id}/${imageJobParamDto.slug}/${imageJobParamDto.key}`;

    // Dynamic import file-type is ESM module
    const fileType = await dynamicImport('file-type');

    // Get the file type from buffer
    const type = await fileType.fileTypeFromBuffer(fileStream);

    // Validate the file type
    if (!IMAGE_FILE_TYPE.includes(type.mime)) {
      throw this.baseExceptionService.exception(
        'INVALID_FILE_TYPE',
        `${type.mime} is in invalid file type`,
      );
    }

    // Upload the file to S3
    // return this.s3Service.uploadFileByPresignedUrl(keyPath);
    return await this.s3Service.uploadFile(fileStream, keyPath);
  }

  async insertJobActivities(
    jobEntity: JobEntity,
    activityType: JobActivitiesType,
    detail: JobActivityDetail,
    user: WithUserContext,
  ) {
    // New job activities entity
    const jobActivities = new JobActivitiesEntity();

    jobActivities.jobId = jobEntity.jobId;
    jobActivities.companyId = jobEntity.companyId;
    jobActivities.type = activityType;
    jobActivities.detail = detail;
    jobActivities.createdBy = user.userKey;

    // Save job activities
    await this.jobActivitiesRepo.save(jobActivities);
  }

  async verifyByDipChip(
    user: WithUserContext,
    jobId: string,
    body: UpdateCustomerInfoJobDto,
  ) {
    const contract = await this.contractsRepo.findOne({
      where: { jobId },
    });
    const jobBody = this.prepareVerifiedJob(
      user,
      CustomerInfoType.DIP_CHIP,
      contract,
    );
    // Provision contract and voucher
    return await this.provisionContractJob(
      jobId,
      jobBody,
      { ...body, type: CustomerInfoType.DIP_CHIP },
      user,
      contract,
    );
  }

  async identityReviewRequest(
    user: WithUserContext,
    jobId: string,
    body: UpdateCustomerInfoJobDto,
  ) {
    const jobBody = this.defaultPrepareJob({
      updatedBy: user.userKey,
      status: JobStatus.IDENTITY_REQUESTED,
    });
    const contract = await this.contractsRepo.findOne({
      where: { jobId },
    });

    if (body.rejectReason) {
      assign(contract, {
        customerInfo: {
          ...contract?.customerInfo,
          rejectReason: body.rejectReason || '',
        },
      });
    }

    // Provision contract and voucher
    const job = await this.provisionContractJob(
      jobId,
      jobBody,
      { ...body, type: CustomerInfoType.IDENTITY_VERIFICATION },
      user,
      contract,
    );

    let isManager = false;
    const userBranchPermission = user.permissions?.find(
      (permission) => permission.branchId === job.branchId,
    );
    if (userBranchPermission) {
      isManager = userBranchPermission.permission.includes(
        Permission.SHOP_OTHERS_OCR_CONFIRM + PermissionAction.UPDATE,
      );
    }

    if (!isManager) {
      this.jobRequestQueue.add('job-request', {
        companyId: user.company,
        jobId,
        type: JobStatus.IDENTITY_REQUESTED,
      });
    }

    return job;
  }

  async updateJob(
    jobId: string,
    companyId: string,
    manager: EntityManager,
    body: Partial<JobEntity>,
  ) {
    const job = await manager.findOne(JobEntity, {
      where: { jobId, companyId: companyId },
    });

    if (!job) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Job not found',
      );
    }

    // Prepare and sanitize job data
    const sanitizeInputBody = this.sanitizeInputBody(body);
    const jobBody = this.computeUpdatePayload(job, sanitizeInputBody);

    // Save the updated job
    return await manager.save(assign(job, jobBody));
  }

  async provisionContractJob(
    jobId: string,
    jobPayload: Partial<JobEntity>,
    body: CustomerInfo,
    user: WithUserContext,
    existingContract: ContractEntity | null,
  ): Promise<JobEntity> {
    const queryRunner = this.jobsRepo.manager.connection.createQueryRunner();

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
      const manager = queryRunner.manager;

      // Update job details
      const job = await this.updateJob(
        jobId,
        user.company,
        manager,
        jobPayload,
      );
      const contract = await this.contractsService.provisionContract(
        manager,
        body,
        job,
        existingContract,
      );

      // Commit transaction
      await queryRunner.commitTransaction();
      return { ...job, contract };
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  async identityReject(
    user: WithUserContext,
    jobId: string,
    body: IdentityRejectedDto,
  ): Promise<JobEntity> {
    const queryRunner = this.jobsRepo.manager.connection.createQueryRunner();

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
      const man = queryRunner.manager;

      // Update job detail
      const updatedJob = await this.updateJob(
        jobId,
        user.company,
        man,
        this.defaultPrepareJob({
          updatedBy: user.userKey,
          status: JobStatus.IDENTITY_REJECTED,
        }),
      );

      // Find contract and voucher
      const contract = await man.findOne(ContractEntity, { where: { jobId } });

      if (!contract) {
        throw this.baseExceptionService.exception(
          'NOT_FOUND_DATA',
          `Contract not found`,
        );
      }

      // Update contract
      const updatedContract = await man.save(
        assign(contract, {
          customerInfo: {
            ...contract.customerInfo,
            rejectReason: body.reason,
          },
          updatedBy: updatedJob.updatedBy,
        }),
      );

      // Commit transaction
      await queryRunner.commitTransaction();
      return {
        ...updatedJob,
        contract: updatedContract,
      };
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  async confirmPurchasedJob(
    user: WithUserContext,
    jobId: string,
    body: ConfirmContractDto,
  ): Promise<JobEntity> {
    // Find job and check job permission
    const currentJob = await this.jobsRepo.findOne({
      where: {
        jobId,
        companyId: user.company,
      },
      relations: ['campaigns'],
    });
    if (!currentJob) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        'Job not found',
      );
    }
    const { status } = currentJob;
    if (status === JobStatus.PURCHASED) {
      return currentJob;
    }
    if (status !== JobStatus.CAMPAIGN_SELECTED) {
      throw this.baseExceptionService.exception(
        'INVALID_INPUT_FOR_UPDATE_JOB',
        `Invalid input for update job - The status must be: ${JobStatus.CAMPAIGN_SELECTED}. Current status is: ${status}`,
      );
    }

    // Create query runner
    const queryRunner = this.jobsRepo.manager.connection.createQueryRunner();
    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
      const man = queryRunner.manager;

      // Loop for campaignRedemptionCode
      const campaignRedemptionCode =
        await this.contractsService.confirmCampaignRedemptionCode({
          manager: man,
          job: currentJob,
        });

      // Update job with the purchased at
      const job = await this.updateJob(
        jobId,
        user.company,
        man,
        this.preparePurchasedJob(user),
      );

      // contract save and generate pdf with code
      const contract = await this.contractsService.confirmContract(
        man,
        job,
        body,
      );

      // Commit transaction
      await queryRunner.commitTransaction();

      // Send contract to email
      job.campaignRedemptionCode = campaignRedemptionCode;
      await this.contractsService.sendContractToEmail(job, contract);
      return {
        ...omit(job, ['campaignRedemptionCode']),
        contract: omit(contract, ['importedVouchers']),
      };
    } catch (err: any) {
      if (queryRunner.isTransactionActive) {
        // Check if transaction is active before rolling back (case error from sendContractToEmail)
        await queryRunner.rollbackTransaction();
      }
      if (
        err?.code === BASE_EXCEPTIONS.CAMPAIGN_REDEMPTION_CODE_NOT_FOUND.code ||
        err?.code === BASE_EXCEPTIONS.CAMPAIGN_NOT_ACTIVE.code
      ) {
        await queryRunner.manager.save(
          assign(currentJob, {
            status: JobStatus.IDENTITY_VERIFIED,
            campaigns: [],
          }),
        );
      }
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  async getEstimationActivities(
    estimationActivityId: string,
    companyId: string,
  ): Promise<any> {
    const estimationActivitiesObj = await this.estimationActivitiesRepo.findOne(
      {
        where: { id: estimationActivityId, companyId },
      },
    );
    if (!estimationActivitiesObj) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA');
    } else {
      if (estimationActivitiesObj.jobId) {
        throw this.baseExceptionService.exception(
          'UNAVAILABLE_ESTIMATION_ACTIVITY',
        );
      }
      //createDate to now more than equal QR_EXPIRE_HOURS hour
      const milliDiff =
        new Date().getTime() - estimationActivitiesObj.createdAt.getTime();
      const hourDiff = Math.floor(milliDiff / 3600000);
      if (hourDiff >= QR_EXPIRE_HOURS) {
        throw this.baseExceptionService.exception(
          'EXPIRED_ESTIMATION_ACTIVITY',
        );
      }
    }
    return {
      firstName: estimationActivitiesObj.firstName,
      lastName: estimationActivitiesObj.lastName,
    };
  }

  /**
   * Maps vendorType to jobVendor value
   * @param vendorType - The vendor type from request body ('MASS' | 'KINGFISHER')
   * @returns The corresponding jobVendor value
   */
  private mapVendorType(vendorType: string): string {
    if (vendorType === 'KINGFISHER') {
      return 'KINGFISHER';
    }
    return vendorType;
  }

  async createFromDraft(
    user: WithUserContext,
    jobId: string,
    body: CreateFromDraftDto,
  ) {
    const oldNotSoldJob = await this.jobsRepo.findOne({
      where: {
        status: Not(
          In([
            JobStatus.DRAFT,
            JobStatus.SOLD,
            JobStatus.REJECT_BY_SHOP,
            JobStatus.REJECT_BY_CUSTOMER,
          ]),
        ),
        jobId: Not(jobId),
        deviceKey: body.deviceKey,
      },
    });

    if (oldNotSoldJob) {
      throw this.baseExceptionService.exception('IMEI_NOT_SOLD');
    }

    const companyId = user.company;
    const colorId = body.colorId;

    const currentJob = await this.jobsRepo.findOne({
      where: {
        jobId,
        companyId,
      },
    });

    //handle fail case
    if (!currentJob) {
      throw this.baseExceptionService.exception(
        'NOT_FOUND_DATA',
        `Job not found`,
      );
    }

    if (currentJob.draftEstimationId !== body.estimationId) {
      throw this.baseExceptionService.exception('INVALID_ESTIMATION_ACTIVITY');
    }

    const currentEstimationActivity =
      await this.estimationActivitiesRepo.findOne({
        where: {
          id: body.estimationId,
          companyId,
        },
      });

    // ea not found
    if (!currentEstimationActivity) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA');
    }

    // ea is already assigned to job
    if (currentEstimationActivity.jobId) {
      throw this.baseExceptionService.exception(
        'UNAVAILABLE_ESTIMATION_ACTIVITY',
      );
    }

    if (colorId !== undefined) {
      // check is color existed in model
      const modelMaster = await this.modelMastersRepo.findOne({
        where: {
          companyId,
          modelKey: currentJob.modelKey,
        },
        relations: { modelMasterColors: true },
      });

      const modelMasterColor = modelMaster?.modelMasterColors?.filter(
        (color) => color.id === colorId,
      );

      if (!modelMasterColor || modelMasterColor.length === 0) {
        throw this.baseExceptionService.exception(
          'BODY_PAYLOAD_INVALID',
          `Invalid colorId`,
        );
      }
    }

    // prepare checklist snapshot data
    const checkList = currentJob.checkList.find(
      (item) => item.slug === 'product_information',
    );

    const productInfoChecklist = checkList
      ? checkList.survey_form?.pages[0]?.elements
      : [];

    if (productInfoChecklist.length === 0) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA');
    }

    const totalRequired = productInfoChecklist.filter(
      (item) => item.isRequired === true,
    ).length;

    // validate question answer
    let isRequiredCount = 0;
    const productionInfoData = body.answers.product_information;

    // loop check matching question and answer
    Object.keys(productionInfoData).forEach((key) => {
      productInfoChecklist.forEach((question) => {
        if (question.name === key) {
          isRequiredCount += question.isRequired ? 1 : 0;

          if (question.type === 'question_selection') {
            if (
              !question.choices.some(
                (item) => item.value === productionInfoData[key],
              )
            ) {
              throw this.baseExceptionService.exception(
                'BODY_PAYLOAD_INVALID',
                'Body payload invalid, answers invalid',
              );
            }
          }

          if (question.type === 'question_option') {
            const optionChoices = question.choices.map((item) => item.value);

            if (Array.isArray(productionInfoData[key])) {
              if (productionInfoData[key].length > 0) {
                if (
                  !productionInfoData[key].every((item) =>
                    optionChoices.includes(item),
                  )
                ) {
                  throw this.baseExceptionService.exception(
                    'BODY_PAYLOAD_INVALID',
                    'Body payload invalid, answers invalid',
                  );
                }
              }
            } else {
              throw this.baseExceptionService.exception(
                'BODY_PAYLOAD_INVALID',
                'Body payload invalid, answers invalid',
              );
            }
          }
        }
      });
    });

    // validate request body has all required question
    if (isRequiredCount !== totalRequired) {
      throw this.baseExceptionService.exception(
        'BODY_PAYLOAD_INVALID',
        'Body payload invalid, questions not complete',
      );
    }

    currentJob.estimationActivities = currentEstimationActivity;
    currentJob.deviceKey = body.deviceKey;
    currentJob.deviceKey2 = body.deviceKey2;
    currentJob.colorId = colorId ?? null;
    currentJob.phoneNumber = body.phoneNumber;
    currentJob.requestedAt = new Date();

    if (body.vendorType) {
      currentJob.vendorType = this.mapVendorType(body.vendorType);
    }
    currentJob.checkListValues = {
      ...currentJob.checkListValues,
      product_information: body.answers.product_information,
      product_images: body.answers.product_images,
      product_additional_information:
        body.answers.product_additional_information,
      product_additional_images: body.answers.product_additional_images,
      media: body.answers.media,
    };

    currentJob.status = JobStatus.QUOTE_REQUESTED;
    await this.jobsRepo.save(currentJob);
    await this.jobRequestQueue.add('job-request', { companyId, jobId });

    return null;
  }

  async getOcrResult(user: WithUserContext, jobId: string, body: PostOcrDto) {
    // strip data uri from base64
    const image = body.image.split('base64,')[1] ?? body.image;

    const bufferImage = Buffer.from(convertBase64ToBytes(image));
    const imagePath = await this.s3Service.uploadFile(
      bufferImage,
      `company/${user.company}/jobs/${jobId}/customer-identification/${body.key}`,
    );

    let ocrResult: any = null;
    let masterAddress: any = null;

    const job = await this.jobsRepo.findOne({
      where: { jobId, companyId: user.company },
    });

    if (!body.excludeOcr) {
      try {
        ocrResult = await this.ocrService.getOcrResult(image, user.company);
        const activity = new GeneralActivitiesEntity();
        activity.type = 'ocr';
        activity.detail = {
          jobId,
          imageKey: body.key,
        };
        activity.createdBy = user.userKey;
        activity.companyId = user.company;
        await this.generalActivitiesRepository.save(activity);
      } catch (err) {
        console.log('OCR API error', err);
        throw this.baseExceptionService.exception('BODY_PAYLOAD_INVALID', {
          imagePath,
        });
      }
    }

    if (ocrResult?.district1 && ocrResult?.district2 && ocrResult?.province) {
      const trimmedSubdistrict = ocrResult.district1.trim();
      const trimmedDistrict = ocrResult.district2.trim();
      const trimmedProvince = ocrResult.province.trim();
      const isBangkok = trimmedProvince === 'กรุงเทพมหานคร';
      const address = await this.masterAddressRepository
        .createQueryBuilder('ma')
        .select([
          'ma.subdistrictcode',
          'ma.districtcode',
          'ma.provincecode',
          'ma.zipcode',
        ])
        .where(`ma.subdistrict ->> 'th' = '${trimmedSubdistrict}'`)
        .andWhere(
          `ma.district ->> 'th' = '${
            isBangkok ? 'เขต' : ''
          }${trimmedDistrict}'`,
        )
        .andWhere(`ma.province ->> 'th' = '${trimmedProvince}'`)
        .getRawOne();

      if (address) {
        masterAddress = {
          subdistrictcode: Number(address.subdistrictcode),
          districtcode: address.districtcode,
          provincecode: address.provincecode,
          zipcode: address.ma_zipcode,
        };
      }
    }

    return {
      ocrResult,
      imagePath,
      masterAddress,
      phoneNumber: job?.phoneNumber,
    };
  }

  async getPresigned(jobId: string, key: string, user: WithUserContext) {
    const keyPath = getS3JobUrlPath(user.company, jobId, key);
    const url = await this.s3Service.getUploadFilePreSignedUrl(keyPath);
    return { url: url, path: keyPath };
  }

  async getMediaUrl(job: JobEntity) {
    const jobChecklist = job.checkListValues;
    const jobChecklistMedia = jobChecklist.media;
    //read from checkListValues.media
    for (const keySection in jobChecklistMedia) {
      //read each key
      for (const subKeySection in jobChecklistMedia[keySection]) {
        const videoPathList = jobChecklistMedia[keySection][subKeySection];
        const urlList: Promise<string>[] = [];
        for (const path of videoPathList) {
          urlList.push(this.s3Service.getFileWithSignedUrl(path));
        }
        const section = {
          ...jobChecklist[keySection],
          media_url: jobChecklist[keySection].media_url ?? {},
        };
        const result = await Promise.all(urlList);
        section['media_url'][subKeySection] = result;
        jobChecklist[keySection] = section;
      }
    }

    // replace videoPath to presignedPath
    if (
      job.incompleteAOListValue &&
      job.incompleteAOListValue.length > 0 &&
      job.aoShippingStatus === AOShippingStatus.LOST
    ) {
      const lastItemData =
        job.incompleteAOListValue[job.incompleteAOListValue.length - 1];

      const presignedUrl = await this.s3Service.getFileWithSignedUrl(
        lastItemData.videoPath ?? '',
      );

      lastItemData.videoPath = presignedUrl;
    }

    return job;
  }

  async getMediaUrlFromPath(body: { path: string }) {
    try {
      const { path } = body;
      const url = await this.s3Service.getFileWithSignedUrl(path);

      return {
        url,
      };
    } catch (err) {
      console.log('error', err);
      throw this.baseExceptionService.exception('BODY_PAYLOAD_INVALID');
    }
  }

  async getCampaignInformation(job: JobEntity) {
    const jobCampaigns = await this.jobsRepo.findOne({
      where: { jobId: job.jobId },
      relations: ['campaigns'],
    });

    if (!jobCampaigns) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA');
    }

    job.campaigns = jobCampaigns.campaigns;
    return job;
  }

  async updateSelectCampaign(
    user: WithUserContext,
    id: string,
    body: { campaignCodes: string[] | null },
  ) {
    const campaignCode = body.campaignCodes ? body.campaignCodes[0] : null;
    const now = new Date();
    const prevDate = new Date(now);
    prevDate.setDate(prevDate.getDate() - 1);

    //find job by id
    const job = await this.jobsRepo.findOne({
      relations: ['createdUser'],
      where: {
        jobId: id,
        companyId: user.company,
      },
    });
    if (!job) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA');
    } else {
      if (job.createdBy !== user.userKey) {
        throw this.baseExceptionService.exception('INVALID_JOB_PERMISSION');
      } else if (job.status !== JobStatus.IDENTITY_VERIFIED) {
        throw this.baseExceptionService.exception('INVALID_JOB_FOR_ACTION');
      }
    }
    //save job case no campaign selected
    if (campaignCode === null) {
      job.status = JobStatus.CAMPAIGN_SELECTED;
      await this.jobsRepo.save(job);
      return job;
    }
    //find campaign by campaignCodes
    const campaign = await this.campaignRepo.findOne({
      where: {
        companyId: user.company,
        campaignCode: campaignCode,
        startDate: LessThanOrEqual(now),
        endDate: MoreThanOrEqual(prevDate),
        isActive: true,
        modelMasters: {
          modelKey: job.modelKey,
        },
      },
    });
    if (!campaign) {
      throw this.baseExceptionService.exception('CAMPAIGN_NOT_FOUND');
    }

    const queryRunner =
      this.campaignRedemptionCodeRepo.manager.connection.createQueryRunner();
    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();
      for (let i = 1; i <= campaign.maxRedemptionCode; i++) {
        const campRedemp = await queryRunner.manager
          .getRepository(CampaignRedemptionCodeEntity)
          .createQueryBuilder('redemption')
          .where(
            `redemption.companyId = :companyId and
          redemption.campaignCode = :code and
          redemption.order = :order and
          redemption.grade @> :grade and
          redemption.jobId is null`,
            {
              code: campaign.campaignCode,
              companyId: user.company,
              order: i,
              grade: [job.currentGrade],
            },
          )
          .getOne();
        if (!campRedemp) {
          throw this.baseExceptionService.exception(
            'CAMPAIGN_REDEMPTION_CODE_NOT_FOUND',
          );
        }
      }
      //update job status
      job.status = JobStatus.CAMPAIGN_SELECTED;
      job.campaigns = [campaign];
      await queryRunner.manager.save(job);
      await queryRunner.commitTransaction();
    } catch (e) {
      await queryRunner.rollbackTransaction();
      await queryRunner.release();
      throw e;
    } finally {
      await queryRunner.release();
    }
    return job;
  }
}

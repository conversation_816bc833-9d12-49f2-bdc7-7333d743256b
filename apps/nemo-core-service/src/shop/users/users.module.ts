import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { WithBranchMiddleware, WithUserMiddleware } from '../../middlewares';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  BranchEntity,
  UserEntity,
  UserRoleBranchEntity,
  UserVendorTypeMappingEntity,
} from '../../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      BranchEntity,
      UserRoleBranchEntity,
      UserVendorTypeMappingEntity,
    ]),
  ],
  controllers: [UsersController],
  providers: [UsersService],
})
export class UsersModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(UsersController);
    consumer
      .apply(WithBranchMiddleware)
      .exclude({ path: '/v1/shop/users/me', method: RequestMethod.GET })
      .forRoutes(UsersController);
  }
}

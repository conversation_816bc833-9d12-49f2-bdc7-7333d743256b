import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ModelMasterEntity,
  CompanyEntity,
  SystemConfigEntity,
  JobEntity,
  ModelMasterFunctionEntity,
  ModelChecklistEntity,
  ModelMasterGradeDetail,
  ChecklistType,
  ModelPriceActivitiesEntity,
  ModelPriceActivityDetail,
  ModelPriceActivityType,
  ModelPriceActivityTable,
} from '../../entities';
import {
  SelectQueryBuilder,
  Repository,
  EntityManager,
  In,
  Like,
} from 'typeorm';
import { Request } from 'express';
import {
  concat,
  forEach,
  includes,
  isEqual,
  omit,
  orderBy,
  reverse,
  uniqBy,
  isEmpty,
  map,
  sortBy,
} from 'lodash';
import {
  ExcelManagerService,
  Options,
  IConvertToType,
} from '../../excel/excel-manager.service';
import { BaseExceptionService } from '../../exceptions';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { reCalculateProduct } from '../../utils/job/queue';
import {
  TypeUpdateDataModelMasterFunctionAction,
  TypeUpdateDataModelMasterGradeGrade,
  UpdateModelMasterDto,
} from './dto/update-model-masters-dto';
import { TypeChecklistFunctionSection } from '../../../src/config';

@Injectable()
export class ModelMastersService {
  constructor(
    @InjectRepository(ModelMasterEntity)
    private readonly modelMastersRepo: Repository<ModelMasterEntity>,
    @InjectRepository(CompanyEntity)
    private readonly companyRepo: Repository<CompanyEntity>,
    @InjectRepository(SystemConfigEntity)
    private readonly systemConfigRepo: Repository<SystemConfigEntity>,
    @InjectRepository(JobEntity)
    private readonly jobsRepo: Repository<JobEntity>,
    @InjectQueue('re-calculate-product-queue')
    private readonly queue: Queue,
    private readonly excelManagerService: ExcelManagerService,
    private readonly baseExceptionService: BaseExceptionService,
    @InjectRepository(ModelChecklistEntity)
    private readonly modelChecklistRepo: Repository<ModelChecklistEntity>,
    @InjectRepository(ModelPriceActivitiesEntity)
    private readonly modelPriceActivitiesRepo: Repository<ModelPriceActivitiesEntity>,
  ) {}

  buildSearchQuery(
    context: Request,
    listQuery: SelectQueryBuilder<ModelMasterEntity>,
  ): SelectQueryBuilder<ModelMasterEntity> {
    const brand = context.query.brand as string;
    const model = context.query.model as string;
    const rom = context.query.rom as string;

    // Construct filter conditions & apply conditions
    const conditions = [
      brand && `r.modelIdentifiers ->> 'brand' = '${brand}'`,
      model && `r.modelIdentifiers ->> 'model' = '${model}'`,
      rom && `r.modelIdentifiers ->> 'rom' = '${rom}'`,
    ].filter(Boolean);

    if (conditions.length) listQuery.andWhere(conditions.join(' AND '));

    return listQuery;
  }

  afterLoad(data: ModelMasterEntity[]): any[] {
    // Transformation after data loading
    const temp: any[] = data.map((d) => ({
      ...d,
      brand: d.modelIdentifiers['brand'],
      model: d.modelIdentifiers['model'],
      rom: d.modelIdentifiers['rom'],
      gradeA: d.modelMasterGrades[0].purchasePrice
        ? Number(d.modelMasterGrades[0].purchasePrice).toFixed(2)
        : d.modelMasterGrades[0].purchasePrice,
      gradeB: d.modelMasterGrades[1].purchasePrice
        ? Number(d.modelMasterGrades[1].purchasePrice).toFixed(2)
        : d.modelMasterGrades[1].purchasePrice,
      gradeC: d.modelMasterGrades[2].purchasePrice
        ? Number(d.modelMasterGrades[2].purchasePrice).toFixed(2)
        : d.modelMasterGrades[2].purchasePrice,
      gradeD: d.modelMasterGrades[3].purchasePrice
        ? Number(d.modelMasterGrades[3].purchasePrice).toFixed(2)
        : d.modelMasterGrades[3].purchasePrice,
      referencePrice:
        d.referencePrice || Number(d.referencePrice) === 0
          ? d.referencePrice?.toFixed(2)
          : d.referencePrice,
      averageRetailCostAA:
        d.averageRetailCost && d.averageRetailCost.AA
          ? Number(d.averageRetailCost.AA).toFixed(2)
          : null,
      averageWholeSaleCostAA:
        d.averageWholeSaleCost && d.averageWholeSaleCost.AA
          ? Number(d.averageWholeSaleCost.AA).toFixed(2)
          : null,
      averageRetailCostBB:
        d.averageRetailCost && d.averageRetailCost.BB
          ? Number(d.averageRetailCost.BB).toFixed(2)
          : null,
      averageWholeSaleCostBB:
        d.averageWholeSaleCost && d.averageWholeSaleCost.BB
          ? Number(d.averageWholeSaleCost.BB).toFixed(2)
          : null,
      averageRetailCostCC:
        d.averageRetailCost && d.averageRetailCost.CC
          ? Number(d.averageRetailCost.CC).toFixed(2)
          : null,
      averageWholeSaleCostCC:
        d.averageWholeSaleCost && d.averageWholeSaleCost.CC
          ? Number(d.averageWholeSaleCost.CC).toFixed(2)
          : null,
      averageRetailCostAD:
        d.averageRetailCost && d.averageRetailCost.AD
          ? Number(d.averageRetailCost.AD).toFixed(2)
          : null,
      averageWholeSaleCostAD:
        d.averageWholeSaleCost && d.averageWholeSaleCost.AD
          ? Number(d.averageWholeSaleCost.AD).toFixed(2)
          : null,
      averageRetailCostBD:
        d.averageRetailCost && d.averageRetailCost.BD
          ? Number(d.averageRetailCost.BD).toFixed(2)
          : null,
      averageWholeSaleCostBD:
        d.averageWholeSaleCost && d.averageWholeSaleCost.BD
          ? Number(d.averageWholeSaleCost.BD).toFixed(2)
          : null,
      averageRetailCostCD:
        d.averageRetailCost && d.averageRetailCost.CD
          ? Number(d.averageRetailCost.CD).toFixed(2)
          : null,
      averageWholeSaleCostCD:
        d.averageWholeSaleCost && d.averageWholeSaleCost.CD
          ? Number(d.averageWholeSaleCost.CD).toFixed(2)
          : null,
      averageRetailCostDD:
        d.averageRetailCost && d.averageRetailCost.DD
          ? Number(d.averageRetailCost.DD).toFixed(2)
          : null,
      averageWholeSaleCostDD:
        d.averageWholeSaleCost && d.averageWholeSaleCost.DD
          ? Number(d.averageWholeSaleCost.DD).toFixed(2)
          : null,
    }));
    return temp;
  }

  exportExcelModelMasterPrice(data: any[]): Promise<Buffer> {
    const sheetName = 'Master Model & Whitelist';
    this.excelManagerService.options = excelManagerOption;

    const dataToExport = data.map((obj) => ({
      ...obj,
      systemCodeList: obj.systemCodeList ? obj.systemCodeList.join(';') : null,
    }));

    return this.excelManagerService.generateExcelFile(dataToExport, sheetName);
  }

  async putModelMaster(
    company: string | undefined,
    file: Express.Multer.File,
    user: string,
  ) {
    this.excelManagerService.options = excelManagerOption;
    const fileData = await this.excelManagerService.readExcelFile(
      file.buffer,
      file.mimetype,
    );
    const saveList: Partial<ModelMasterEntity>[] = [];

    const companyData = await this.companyRepo.findOne({
      where: { companyId: company },
    });

    const activityLog = new ModelPriceActivitiesEntity();
    const logDetail: ModelPriceActivityDetail[] = [];

    if (companyData) {
      for (const value of uniqBy(fileData, 'modelKey')) {
        const saveData = new ModelMasterEntity();

        saveData.companyId = companyData.companyId;
        saveData.modelKey = value.modelKey;
        saveData.modelIdentifiers = {
          rom: value.rom,
          brand: value.brand,
          model: value.model,
        };
        saveData.templateId = 'v1';
        saveData.matCode = value.matCode;
        saveData.referencePrice = value.referencePrice;
        saveData.purchasedRatio = value.purchasedRatio;
        saveData.modelYear = value.modelYear;
        if (value.systemCodeList) {
          saveData.systemCodeList = value.systemCodeList
            .split(';')
            .map((e) => e.trim())
            .filter((e) => e.length > 0);
        } else {
          saveData.systemCodeList = value.systemCodeList;
        }
        saveData.modelImageUrl = value.modelImageUrl;
        saveData.ownerName = value.ownerName;
        saveData.createdBy = user;
        saveData.updatedBy = user;

        const modelMasterGrades = [
          {
            grade: 'A',
            purchasePrice: value.gradeA,
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'B',
            purchasePrice: value.gradeB,
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'C',
            purchasePrice: value.gradeC,
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
          {
            grade: 'D',
            purchasePrice: value.gradeD,
            lastPurchasedOn: 'ISO8601',
            lastPurchasedPrice: 'Grade Info',
          },
        ];

        saveData.modelMasterGrades = modelMasterGrades;

        // generate log detail
        logDetail.push({
          companyId: companyData.companyId,
          modelKey: value.modelKey,
          modelMaster: {
            modelIdentifiers: saveData.modelIdentifiers,
            matCode: saveData.matCode,
            modelImageUrl: saveData.modelImageUrl,
            modelPrice: saveData.referencePrice,
            releaseYear: saveData.modelYear,
            systemCodeList: saveData.systemCodeList ?? [],
            modelMasterGrades: modelMasterGrades ?? [],
            percentPurchase: saveData.purchasedRatio?.toString(),
            ownerName: saveData.ownerName,
          },
          modelMasterFunction: [],
        });

        saveList.push(saveData);
      }

      activityLog.type = ModelPriceActivityType.FILE;
      activityLog.table = ModelPriceActivityTable.MODEL_MASTER;
      activityLog.detail = logDetail;
      activityLog.companyId = companyData.companyId;
      activityLog.createdBy = user;
      activityLog.createdAt = new Date();
    }

    await this.modelMastersRepo.manager.transaction(
      async (man: EntityManager): Promise<void> => {
        for (let i = 0; i < saveList.length; i += 1000) {
          await man
            .createQueryBuilder()
            .insert()
            .into(ModelMasterEntity)
            .values(saveList.slice(i, i + 1000))
            .orUpdate(
              [
                'model_identifiers',
                'template_id',
                'model_master_grades',
                'model_export_details',
                'updated_at',
                'mat_code',
                'reference_price',
                'model_year',
                'purchased_ratio',
                'updated_by',
                'system_code',
                'model_image_url',
                'system_code_list',
                'owner_name',
              ],
              ['company_id', 'model_key'],
            )
            .execute();
        }
      },
    );

    await this.modelPriceActivitiesRepo.save(activityLog);

    return null;
  }

  async uploadAverageCost({
    file,
    user,
    company,
  }: {
    file: Express.Multer.File;
    user: string;
    company: string;
  }) {
    this.excelManagerService.options = avgCostOption;

    const fileData = await this.excelManagerService.readExcelFile(
      file.buffer,
      file.mimetype,
    );

    //map model key and object
    const mapModelKeyValue: any = {};
    const modelKeyList: string[] = [];

    uniqBy(fileData, 'modelKey').forEach((item) => {
      const { modelKey } = item;
      mapModelKeyValue[modelKey] = item;
      modelKeyList.push(modelKey);
    });

    // search all modelMaster by modelKeyList
    const modelMasterList = await this.modelMastersRepo.find({
      where: { companyId: company, modelKey: In(modelKeyList) },
    });

    if (modelKeyList.length !== modelMasterList.length) {
      const setExistKeys = new Set();
      modelMasterList.forEach((model) => setExistKeys.add(model.modelKey));
      const notExistKeys = modelKeyList.filter((key) => !setExistKeys.has(key));
      if (notExistKeys.length > 0) {
        throw this.baseExceptionService.exception('KEY_NOT_EXIST', {
          keys: notExistKeys,
        });
      }
    }

    // loop modelMaster
    const updatedAt = new Date();
    modelMasterList.forEach((modelMaster) => {
      const { modelKey } = modelMaster;
      const excelData = mapModelKeyValue[modelKey];

      const averageRetailCostJson = {
        AA: excelData.averageRetailCostAA,
        BB: excelData.averageRetailCostBB,
        CC: excelData.averageRetailCostCC,
        AD: excelData.averageRetailCostAD,
        BD: excelData.averageRetailCostBD,
        CD: excelData.averageRetailCostCD,
        DD: excelData.averageRetailCostDD,
      };

      const averageWholeSaleCostJson = {
        AA: excelData.averageWholeSaleCostAA,
        BB: excelData.averageWholeSaleCostBB,
        CC: excelData.averageWholeSaleCostCC,
        AD: excelData.averageWholeSaleCostAD,
        BD: excelData.averageWholeSaleCostBD,
        CD: excelData.averageWholeSaleCostCD,
        DD: excelData.averageWholeSaleCostDD,
      };

      modelMaster.matCodeSale = excelData.matCodeSale;
      modelMaster.insuranceCost = excelData.insuranceCost;
      modelMaster.averageRetailCost = averageRetailCostJson;
      modelMaster.averageWholeSaleCost = averageWholeSaleCostJson;
      modelMaster.updatedBy = user;
      modelMaster.updatedAt = updatedAt;
    });

    const count = modelMasterList.length;
    //--- save to database
    await this.modelMastersRepo.manager.transaction(
      async (man: EntityManager): Promise<void> => {
        for (let i = 0; i < count; i += 1000) {
          const _ = await man
            .createQueryBuilder()
            .insert()
            .into(ModelMasterEntity)
            .values(modelMasterList.slice(i, i + 1000))
            .orUpdate(
              [
                'mat_code_sale',
                'insurance_cost',
                'average_retail_cost',
                'average_whole_sale_cost',
                'updated_at',
                'updated_by',
              ],
              ['company_id', 'model_key'],
            )
            .execute();
        }
      },
    );
    const result = await this.systemConfigRepo.findOne({
      where: { companyId: company, configKey: 'operation_cost' },
    });
    await reCalculateProduct(
      result?.data ? result.data : {},
      this.queue,
      this.jobsRepo,
    );
    return { count };
  }

  exportAvgCost(data: any[]): Promise<Buffer> {
    const sheetName = 'Avg Cost';
    const toExcelData: any = data.map((item: any) => {
      const { insuranceCost } = item;
      const toUpdateDataItem: any = {
        insuranceCost: Number(insuranceCost).toFixed(2),
      };
      return { ...item, ...toUpdateDataItem };
    });

    this.excelManagerService.options = avgCostOption;
    return this.excelManagerService.generateExcelFile(toExcelData, sheetName);
  }

  async updateModelMaster(
    modelKey: string,
    body: UpdateModelMasterDto,
    user: string,
  ) {
    let errorMsg: string[] = [];
    const model = await this.modelMastersRepo.findOneBy({
      modelKey,
    });

    if (!model) {
      throw this.baseExceptionService.exception('NOT_FOUND_DATA', [
        'MASTER_MODEL_NOT_FOUND',
      ]);
    }

    const actionUser = user;
    const actionAt = new Date();
    const { companyId, modelMaster, modelMasterFunction } = body;

    const msgVaildateGrade = this.validateModelMasterGrades(
      modelMaster.modelMasterGrades,
    );

    const saveActivityData = new ModelPriceActivitiesEntity();
    saveActivityData.type = ModelPriceActivityType.WEB;
    saveActivityData.table = ModelPriceActivityTable.MODEL_MASTER;
    saveActivityData.detail = [body];
    saveActivityData.createdBy = actionUser;
    saveActivityData.companyId = companyId;

    const saveData = new ModelMasterEntity();
    Object.assign(saveData, model);
    Object.assign(saveData, modelMaster);
    saveData.updatedBy = actionUser;
    saveData.updatedAt = actionAt;

    // find ModelCheckList
    const modelChecklistData = await this.modelChecklistRepo.find({
      where: { companyId },
    });
    const modelChecklistIds: Record<string, string> = {};
    const modelChecklistChoices: Record<string, string[]> = {};

    for (let i = 0; i < modelChecklistData.length; i++) {
      const item = modelChecklistData[i];
      modelChecklistIds[`${item.functionSection}.${item.functionKey}`] =
        item.id;

      let functionKeyCond: string[] = [];
      if (item.checklistType === ChecklistType.MODULE) {
        functionKeyCond = ['functional', 'non_functional', 'skip'];
      } else {
        functionKeyCond = map(item.questionChoices, 'id');
        functionKeyCond.push('skip');
      }

      modelChecklistChoices[`${item.functionSection}.${item.functionKey}`] =
        functionKeyCond;
    }

    const createModelMasterFunction: ModelMasterFunctionEntity[] = [];
    const checkCreateChoice: Record<string, string[]> = {};
    const checkDeleteChoice: Record<string, string[]> = {};
    const updateModelMasterFunction: ModelMasterFunctionEntity[] = [];
    const deleteModelFunctionKeyCond: string[] = [];
    let isVaildAction: boolean = true;
    let isVaildPattern: boolean = true;
    let isVaildKeyCond: boolean = true;
    const regex = new RegExp(/^-?\d\d*(\.00)?$/);

    for (const data of modelMasterFunction) {
      const { keyCond, penalties, action } = data;
      if (
        !includes(
          Object.values(TypeUpdateDataModelMasterFunctionAction),
          action,
        )
      ) {
        isVaildAction = false;
        continue;
      }

      if (!regex.test(penalties)) {
        isVaildPattern = false;
      }

      const upsertFunctionData = new ModelMasterFunctionEntity();
      upsertFunctionData.functionKeyCond = keyCond;
      upsertFunctionData.penalties = penalties;
      upsertFunctionData.companyId = companyId;
      upsertFunctionData.modelKey = modelKey;
      upsertFunctionData.updatedBy = actionUser;
      upsertFunctionData.updatedAt = actionAt;

      const fnKey = keyCond.split('=')[0];
      const fnChoice = keyCond.split('=')[1];

      if (isEmpty(keyCond)) {
        isVaildKeyCond = false;
      }

      switch (action) {
        case TypeUpdateDataModelMasterFunctionAction.CREATE:
          checkCreateChoice[fnKey] = [
            ...(checkCreateChoice[fnKey] || []),
            fnChoice,
          ];

          if (!modelChecklistIds[fnKey]) {
            isVaildKeyCond = false;
          }

          upsertFunctionData.checkListId = modelChecklistIds[fnKey];
          upsertFunctionData.createdBy = actionUser;

          createModelMasterFunction.push(upsertFunctionData);
          break;

        case TypeUpdateDataModelMasterFunctionAction.UPDATE:
          updateModelMasterFunction.push(upsertFunctionData);
          break;

        case TypeUpdateDataModelMasterFunctionAction.DELETE:
          checkDeleteChoice[fnKey] = [
            ...(checkDeleteChoice[fnKey] || []),
            fnChoice,
          ];

          deleteModelFunctionKeyCond.push(keyCond);
          break;
      }
    }

    let isValidateMasterFunc: boolean = true;
    for (const [key, value] of Object.entries(checkCreateChoice)) {
      const check = this.validateCreateModelMasterFunction(
        modelChecklistChoices[key],
        value,
      );
      if (!check) {
        isValidateMasterFunc = false;
      }
    }
    for (const [key, value] of Object.entries(checkDeleteChoice)) {
      const check = this.validateCreateModelMasterFunction(
        modelChecklistChoices[key],
        value,
      );
      if (!check) {
        isValidateMasterFunc = false;
      }
    }

    const queryRunner =
      this.modelMastersRepo.manager.connection.createQueryRunner();

    try {
      // Connection query runner
      await queryRunner.connect();

      // Start transaction
      await queryRunner.startTransaction();

      await queryRunner.manager.save(
        ModelMasterEntity,
        omit(saveData, 'modelMasterFunction'),
      );

      await queryRunner.manager.save(
        ModelMasterFunctionEntity,
        createModelMasterFunction,
      );

      for (const updataData of updateModelMasterFunction) {
        await queryRunner.manager.update(
          ModelMasterFunctionEntity,
          {
            companyId,
            modelKey,
            functionKeyCond: updataData.functionKeyCond,
          },
          updataData,
        );
      }

      await queryRunner.manager.delete(ModelMasterFunctionEntity, {
        companyId,
        modelKey,
        functionKeyCond: In(deleteModelFunctionKeyCond),
      });

      const countQuestion = await queryRunner.manager.count(
        ModelMasterFunctionEntity,
        {
          where: {
            companyId,
            modelKey,
            functionKeyCond: Like(
              `${TypeChecklistFunctionSection.PRODUCT_INFORMATION}.%`,
            ),
          },
        },
      );

      const countModule = await queryRunner.manager.count(
        ModelMasterFunctionEntity,
        {
          where: {
            companyId,
            modelKey,
            functionKeyCond: Like(
              `${TypeChecklistFunctionSection.REMOBIE_CHECK_LIST}.%`,
            ),
          },
        },
      );

      const msgValidateErr = this.validateModelMaster(
        countQuestion,
        countModule,
        isVaildAction,
        isVaildPattern,
        isVaildKeyCond,
        isValidateMasterFunc,
      );

      errorMsg = concat(errorMsg, msgVaildateGrade, msgValidateErr);

      if (errorMsg.length > 0) {
        throw this.baseExceptionService.exception(
          'BODY_PAYLOAD_INVALID',
          errorMsg,
        );
      }

      await queryRunner.commitTransaction();
      await queryRunner.manager.save(
        ModelPriceActivitiesEntity,
        saveActivityData,
      );
    } catch (error) {
      // Rollback transaction when error query
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }

    return null;
  }

  validateModelMasterGrades(gradeData: ModelMasterGradeDetail[]): string[] {
    const result: string[] = [];
    const gradePurchasePrice = new Map<string, string>();
    forEach(gradeData, (d: ModelMasterGradeDetail) => {
      if (
        includes(Object.values(TypeUpdateDataModelMasterGradeGrade), d.grade)
      ) {
        gradePurchasePrice.set(d.grade, d.purchasePrice);
      }
    });

    const sortedMap = new Map(
      orderBy(Array.from(gradePurchasePrice), [0], ['desc']),
    );

    let isVaildGrade: boolean = true;
    const sortedMapPriceKey = reverse(Array.from(sortedMap.keys()));

    if (
      sortedMap.size !== 4 ||
      !isEqual(
        sortedMapPriceKey,
        Object.values(TypeUpdateDataModelMasterGradeGrade),
      )
    ) {
      isVaildGrade = false;
    }

    let latestPrice = 0;
    let isVaildPattern: boolean = true;
    let isVaildPrice: boolean = true;

    const regex = new RegExp(/^[1-9]\d*(\.\d{2})?$/);

    for (const value of sortedMap.values()) {
      if (!regex.test(value)) {
        isVaildPattern = false;
      }

      if (Number(value) > latestPrice) {
        latestPrice = Number(value);
      } else {
        isVaildPrice = false;
      }
    }

    if (!isVaildPattern) result.push('INPUT_POSITIVE_NUMBER_INVALID');
    if (!isVaildPrice) result.push('PURCHASE_PRICE_INVALID');
    if (!isVaildGrade) result.push('GRADE_INVALID');

    return result;
  }

  validateModelMaster(
    countQuestion: number,
    countModule: number,
    isVaildAction: boolean,
    isVaildPattern: boolean,
    isVaildKeyCond: boolean,
    isValidateMasterFunc: boolean,
  ): string[] {
    const result: string[] = [];
    if (countQuestion < 1) {
      result.push('QUESTION_AT_LEAST_1');
    }

    if (countModule < 1) {
      result.push('CHECK_LIST_AT_LEAST_1');
    }

    if (!isVaildAction) {
      result.push('ACTION_INVALID');
    }

    if (!isVaildPattern) {
      result.push('INPUT_INTEGER_INVALID');
    }

    if (!isVaildKeyCond) {
      result.push('FUNCTION_KEY_COND_INVALID');
    }

    if (!isValidateMasterFunc) {
      result.push('FUNCTION_INPUT_INVALID');
    }

    return result;
  }

  validateCreateModelMasterFunction(allChoice: string[], data: any[]): boolean {
    return isEqual(sortBy(data), sortBy(allChoice)) ? true : false;
  }
}

export const avgCostOption: Options = {
  headers: {
    PRODUCT_ID: {
      keyName: 'modelKey',
      type: IConvertToType.string,
      isRequired: true,
    },
    MATERIAL_ID: {
      keyName: 'matCodeSale',
      type: IConvertToType.string,
      isRequired: true,
    },
    BRAND: {
      keyName: 'brand',
      type: IConvertToType.string,
      isRequired: false,
    },
    MODEL: {
      keyName: 'model',
      type: IConvertToType.string,
      isRequired: false,
    },
    CAPACITY: {
      keyName: 'rom',
      type: IConvertToType.string,
      isRequired: false,
    },
    INSURANCE_COST: {
      keyName: 'insuranceCost',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    A_A_AVG_RETAIL: {
      keyName: 'averageRetailCostAA',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    A_A_AVG_WHOLESALE: {
      keyName: 'averageWholeSaleCostAA',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    B_B_AVG_RETAIL: {
      keyName: 'averageRetailCostBB',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    B_B_AVG_WHOLESALE: {
      keyName: 'averageWholeSaleCostBB',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    C_C_AVG_RETAIL: {
      keyName: 'averageRetailCostCC',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    C_C_AVG_WHOLESALE: {
      keyName: 'averageWholeSaleCostCC',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    A_D_AVG_RETAIL: {
      keyName: 'averageRetailCostAD',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    A_D_AVG_WHOLESALE: {
      keyName: 'averageWholeSaleCostAD',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    B_D_AVG_RETAIL: {
      keyName: 'averageRetailCostBD',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    B_D_AVG_WHOLESALE: {
      keyName: 'averageWholeSaleCostBD',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    C_D_AVG_RETAIL: {
      keyName: 'averageRetailCostCD',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    C_D_AVG_WHOLESALE: {
      keyName: 'averageWholeSaleCostCD',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    D_D_AVG_RETAIL: {
      keyName: 'averageRetailCostDD',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
    D_D_AVG_WHOLESALE: {
      keyName: 'averageWholeSaleCostDD',
      type: IConvertToType.numString,
      isRequired: true,
      options: {
        decimal: 2,
        min: { value: -0.01 },
      },
    },
  },
};

export const excelManagerOption: Options = {
  headers: {
    PRODUCT_ID: {
      keyName: 'modelKey',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'ID',
    },
    OWNER_NAME: {
      keyName: 'ownerName',
      type: IConvertToType.stringKey,
      isRequired: true,
      subHeader: 'Owner name',
      options: {
        maxLength: 30,
      },
    },
    MATERIAL_ID: {
      keyName: 'matCode',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'MatID',
    },
    SYSTEM_CODE: {
      keyName: 'systemCodeList',
      type: IConvertToType.string,
      isRequired: false,
      subHeader: 'System code',
    },
    BRAND: {
      keyName: 'brand',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'ยี่ห้อ',
    },
    MODEL: {
      keyName: 'model',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'รุ่น',
    },
    CAPACITY: {
      keyName: 'rom',
      type: IConvertToType.string,
      isRequired: true,
      subHeader: 'ความจุ',
    },
    MODEL_IMAGE_URL: {
      keyName: 'modelImageUrl',
      type: IConvertToType.string,
      isRequired: false,
      subHeader: 'Image URL',
    },
    MODEL_PRICE: {
      keyName: 'referencePrice',
      type: IConvertToType.numString,
      isRequired: false,
      subHeader: 'ราคาเครื่อง',
      options: {
        decimal: 2,
      },
    },
    RELEASE_YEAR: {
      keyName: 'modelYear',
      type: IConvertToType.numString,
      isRequired: false,
      subHeader: 'ปี',
      options: {
        decimal: 0,
        min: { value: 0 },
        max: { value: 10000 },
      },
    },
    PERCENT_PURCHASE: {
      keyName: 'purchasedRatio',
      type: IConvertToType.number,
      isRequired: false,
      subHeader: '% รับซื้อ',
      options: {
        decimal: 0,
        min: { value: -1 },
        max: { value: 101 },
      },
    },
    MAXIMUM_PURCHASE_PRICE_GRADE_A: {
      keyName: 'gradeA',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ราคารับซื้อสูงสุดของเกรด A',
      options: {
        decimal: 2,
        min: { referenceField: 'MAXIMUM_PURCHASE_PRICE_GRADE_B' },
      },
    },
    MAXIMUM_PURCHASE_PRICE_GRADE_B: {
      keyName: 'gradeB',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ราคารับซื้อสูงสุดของเกรด B',
      options: {
        decimal: 2,
        max: { referenceField: 'MAXIMUM_PURCHASE_PRICE_GRADE_A' },
        min: { referenceField: 'MAXIMUM_PURCHASE_PRICE_GRADE_C' },
      },
    },
    MAXIMUM_PURCHASE_PRICE_GRADE_C: {
      keyName: 'gradeC',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ราคารับซื้อสูงสุดของเกรด C',
      options: {
        decimal: 2,
        max: { referenceField: 'MAXIMUM_PURCHASE_PRICE_GRADE_B' },
        min: { referenceField: 'MAXIMUM_PURCHASE_PRICE_GRADE_D' },
      },
    },
    MAXIMUM_PURCHASE_PRICE_GRADE_D: {
      keyName: 'gradeD',
      type: IConvertToType.numString,
      isRequired: true,
      subHeader: 'ราคารับซื้อสูงสุดของเกรด D',
      options: {
        decimal: 2,
        max: { referenceField: 'MAXIMUM_PURCHASE_PRICE_GRADE_C' },
        min: { value: 0 },
      },
    },
  },
};

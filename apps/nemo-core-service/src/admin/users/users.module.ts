import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { WithUserMiddleware } from '../../middlewares';
import { AdminUsersController } from './users.controller';
import { AdminUsersService, userOption } from './users.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  BranchEntity,
  CompanyEntity,
  CompanyRoleEntity,
  GeneralActivitiesEntity,
  RoleEntity,
  UserEntity,
  UserRoleBranchEntity,
  UserVendorTypeMappingEntity,
} from '../../entities';
import { ExcelManagerModule } from '../../excel/excel-manager.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      BranchEntity,
      CompanyEntity,
      CompanyRoleEntity,
      RoleEntity,
      GeneralActivitiesEntity,
      UserRoleBranchEntity,
      UserVendorTypeMappingEntity,
    ]),
    ExcelManagerModule.register(userOption),
  ],
  controllers: [AdminUsersController],
  providers: [AdminUsersService],
})
export class AdminUsersModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(WithUserMiddleware).forRoutes(AdminUsersController);
  }
}

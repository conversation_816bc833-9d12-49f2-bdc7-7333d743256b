import { SchemaObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { GetJobsRequestAdmin } from 'contracts';

import { createZodDto, zodToOpenAPI } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';
import { toZod } from 'tozod';

const GetJobsSchema: toZod<GetJobsRequestAdmin> = z.object({
  deviceKey: z.string().optional(),
  deliveryOrderId: z.string().optional(),
  status: z.array(z.string()).optional(),
  shippingStatus: z.array(z.string()).optional(),
  search: z.string().optional(),
  orderBy: z.string().optional(),
  page: z.string().optional(),
  pageSize: z.string().optional(),
  myJob: z.string().optional(),
  partialReceived: z.string().optional(),
  isCheckVendorType: z.string().optional(),
});

export const GetJobsOpenApi = zodToOpenAPI(GetJobsSchema) as SchemaObject;

export class GetJobsDto extends createZodDto(GetJobsSchema) {}

import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getQueueToken } from '@nestjs/bull';
import { JobRequestQueueConsumer } from '../../src/shop/jobs/job-request-queue.consumer';
import {
  JobEntity,
  UserEntity,
  SystemConfigEntity,
  UserVendorTypeMappingEntity,
  JobStatus,
} from '../../src/entities';
import { Queue } from 'bull';
import { Repository } from 'typeorm';

describe('JobRequestQueueConsumer', () => {
  let consumer: JobRequestQueueConsumer;
  let jobRepo: Repository<JobEntity>;
  let userRepo: Repository<UserEntity>;
  let systemConfigRepo: Repository<SystemConfigEntity>;
  let userVendorTypeMappingRepo: Repository<UserVendorTypeMappingEntity>;
  let emailQueue: Queue;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [],
      providers: [
        JobRequestQueueConsumer,
        {
          provide: getRepositoryToken(JobEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(UserEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(SystemConfigEntity),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(UserVendorTypeMappingEntity),
          useClass: Repository,
        },
        {
          provide: getQueueToken('email-queue'),
          useValue: {
            add: jest.fn(),
          },
        },
      ],
    }).compile();

    consumer = module.get<JobRequestQueueConsumer>(JobRequestQueueConsumer);
    jobRepo = module.get<Repository<JobEntity>>(getRepositoryToken(JobEntity));
    userRepo = module.get<Repository<UserEntity>>(
      getRepositoryToken(UserEntity),
    );
    systemConfigRepo = module.get<Repository<SystemConfigEntity>>(
      getRepositoryToken(SystemConfigEntity),
    );
    userVendorTypeMappingRepo = module.get<Repository<UserVendorTypeMappingEntity>>(
      getRepositoryToken(UserVendorTypeMappingEntity),
    );
    emailQueue = module.get<Queue>(getQueueToken('email-queue'));
  });

  it('should be defined', () => {
    expect(consumer).toBeDefined();
  });

  describe('jobRequest', () => {
    afterEach(() => {
      jest.clearAllMocks();
    });
    // mock default case
    const DELAY_TIME = 1000;
    const jobData = {
      companyId: 'company1',
      jobId: 'job1',
      type: JobStatus.QUOTE_REQUESTED,
    };
    const job = { data: jobData, queue: { add: jest.fn() } } as any;
    const targetJob = {
      jobId: 'job1',
      companyId: 'company1',
      status: JobStatus.QUOTE_REQUESTED,
      branchId: 'branch1',
      vendorType: 'VENDOR_A',
    } as JobEntity;
    const targetUsers = [
      { email: '<EMAIL>' },
      { email: '<EMAIL>' },
      {},
    ] as UserEntity[];
    const configBaseUrlData = {
      data: { cms: 'http://example.com', frontshop: 'http://example2.com' },
    } as SystemConfigEntity;
    const configSlaData = {
      data: { priceEstimatorJob: DELAY_TIME, managerApprove: DELAY_TIME },
    } as SystemConfigEntity;
    const userVendorMapping = {
      vendorType: 'VENDOR_A',
      userType: 'ESTIMATOR',
    } as UserVendorTypeMappingEntity;

    it('should process job request and send email', async () => {
      jest.spyOn(jobRepo, 'findOne').mockResolvedValueOnce(targetJob);
      jest.spyOn(userVendorTypeMappingRepo, 'findOne').mockResolvedValueOnce(userVendorMapping);
      jest.spyOn(userRepo, 'find').mockResolvedValueOnce(targetUsers);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configBaseUrlData);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configSlaData);

      await consumer.jobRequest(job);

      expect(emailQueue.add).toHaveBeenCalled();
      expect(job.queue.add).toHaveBeenCalledWith('job-request', jobData, {
        delay: DELAY_TIME,
      });
    });

    it.each([
      [JobStatus.QUOTE_REQUESTED],
      [JobStatus.IDENTITY_REQUESTED],
      ['NOT_EXISTS'],
      [null],
    ])('should process job request for each type', async (type) => {
      const newTargetJob = {
        ...targetJob,
        status: type,
      } as JobEntity;
      const newJobData = {
        ...jobData,
        type,
      };
      jest.spyOn(jobRepo, 'findOne').mockResolvedValueOnce(newTargetJob);
      jest.spyOn(userVendorTypeMappingRepo, 'findOne').mockResolvedValueOnce(userVendorMapping);
      jest.spyOn(userRepo, 'find').mockResolvedValueOnce(targetUsers);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configBaseUrlData);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configSlaData);

      await consumer.jobRequest({ ...job, data: newJobData });

      if (type && type !== 'NOT_EXISTS') {
        expect(emailQueue.add).toHaveBeenCalled();
        expect(job.queue.add).toHaveBeenCalledWith('job-request', newJobData, {
          delay: DELAY_TIME,
        });
      }
    });

    it.each([
      [
        {
          data: {},
        } as SystemConfigEntity,
      ],
      [null],
    ])(
      'should not process job request but send email if only no sla time',
      async (slaConfig) => {
        jest.spyOn(jobRepo, 'findOne').mockResolvedValueOnce(targetJob);
        jest.spyOn(userVendorTypeMappingRepo, 'findOne').mockResolvedValueOnce(userVendorMapping);
        jest.spyOn(userRepo, 'find').mockResolvedValueOnce(targetUsers);
        jest
          .spyOn(systemConfigRepo, 'findOne')
          .mockResolvedValueOnce(configBaseUrlData);
        jest
          .spyOn(systemConfigRepo, 'findOne')
          .mockResolvedValueOnce(slaConfig);

        await consumer.jobRequest(job);

        expect(emailQueue.add).toHaveBeenCalled();
        expect(job.queue.add).not.toHaveBeenCalled();
      },
    );

    it('should not process job request or send email if no target job found', async () => {
      jest.spyOn(jobRepo, 'findOne').mockResolvedValueOnce(null);
      jest.spyOn(userVendorTypeMappingRepo, 'findOne').mockResolvedValueOnce(userVendorMapping);
      jest.spyOn(userRepo, 'find').mockResolvedValueOnce(targetUsers);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configBaseUrlData);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configSlaData);

      await consumer.jobRequest(job);

      expect(emailQueue.add).not.toHaveBeenCalled();
      expect(job.queue.add).not.toHaveBeenCalled();
    });

    it('should not process job request or send email if no target users found', async () => {
      jest.spyOn(jobRepo, 'findOne').mockResolvedValueOnce(targetJob);
      jest.spyOn(userVendorTypeMappingRepo, 'findOne').mockResolvedValueOnce(userVendorMapping);
      jest.spyOn(userRepo, 'find').mockResolvedValueOnce([]);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configBaseUrlData);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configSlaData);

      await consumer.jobRequest(job);

      expect(emailQueue.add).not.toHaveBeenCalled();
      expect(job.queue.add).not.toHaveBeenCalled();
    });

    it('should not process job request or send email if no base URL found', async () => {
      jest.spyOn(jobRepo, 'findOne').mockResolvedValueOnce(targetJob);
      jest.spyOn(userVendorTypeMappingRepo, 'findOne').mockResolvedValueOnce(userVendorMapping);
      jest.spyOn(userRepo, 'find').mockResolvedValueOnce(targetUsers);
      jest.spyOn(systemConfigRepo, 'findOne').mockResolvedValueOnce(null);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configSlaData);

      await consumer.jobRequest(job);

      expect(emailQueue.add).not.toHaveBeenCalled();
      expect(job.queue.add).not.toHaveBeenCalled();
    });

    it('should not process job request or send email if no user vendor mapping found', async () => {
      jest.spyOn(jobRepo, 'findOne').mockResolvedValueOnce(targetJob);
      jest.spyOn(userVendorTypeMappingRepo, 'findOne').mockResolvedValueOnce(null);
      jest.spyOn(userRepo, 'find').mockResolvedValueOnce(targetUsers);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configBaseUrlData);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configSlaData);

      await consumer.jobRequest(job);

      expect(emailQueue.add).toHaveBeenCalled();
      expect(job.queue.add).toHaveBeenCalledWith('job-request', jobData, {
        delay: DELAY_TIME,
      });
    });

    it('should handle case when user vendor mapping has no userType', async () => {
      const mappingWithoutUserType = {
        vendorType: 'VENDOR_A',
        userType: null,
      } as any;

      jest.spyOn(jobRepo, 'findOne').mockResolvedValueOnce(targetJob);
      jest.spyOn(userVendorTypeMappingRepo, 'findOne').mockResolvedValueOnce(mappingWithoutUserType);
      jest.spyOn(userRepo, 'find').mockResolvedValueOnce(targetUsers);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configBaseUrlData);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configSlaData);

      await consumer.jobRequest(job);

      expect(userRepo.find).toHaveBeenCalledWith({
        where: {
          companyId: 'company1',
          userType: null,
          userRoleBranch: {
            role: {
              rolePermissions: {
                permissionId: expect.any(String),
                update: true,
              },
            },
          },
        },
      });
      expect(emailQueue.add).toHaveBeenCalled();
    });

    it('should handle IDENTITY_REQUESTED job type with different user conditions', async () => {
      const identityRequestedJob = {
        ...targetJob,
        status: JobStatus.IDENTITY_REQUESTED,
      } as JobEntity;
      const identityJobData = {
        ...jobData,
        type: JobStatus.IDENTITY_REQUESTED,
      };

      jest.spyOn(jobRepo, 'findOne').mockResolvedValueOnce(identityRequestedJob);
      jest.spyOn(userVendorTypeMappingRepo, 'findOne').mockResolvedValueOnce(userVendorMapping);
      jest.spyOn(userRepo, 'find').mockResolvedValueOnce(targetUsers);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configBaseUrlData);
      jest
        .spyOn(systemConfigRepo, 'findOne')
        .mockResolvedValueOnce(configSlaData);

      await consumer.jobRequest({ ...job, data: identityJobData });

      expect(userRepo.find).toHaveBeenCalledWith({
        where: {
          companyId: 'company1',
          userRoleBranch: {
            role: {
              rolePermissions: {
                permissionId: expect.any(String),
                update: true,
              },
            },
            branchId: 'branch1',
          },
        },
      });
      expect(emailQueue.add).toHaveBeenCalled();
    });
  });
});

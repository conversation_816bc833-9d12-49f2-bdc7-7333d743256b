import {
  BranchEntity,
  CompanyEntity,
  ContractEntity,
  CustomerInfoType,
  DeliveryOrderStatus,
  JobEntity,
  JobStatus,
  DeliveryOrderEntity,
  QCStatus,
  AOShippingStatus,
  AllocationOrderStatus,
} from '../../src/entities';
import { mockCheckListValues, mockCheckList } from './checklist';
import { mockModelMasters } from './model-master';
import { mockBase64Jpg } from './base64';
import { mockUserShopEntity } from './user';
import { WithUserContext } from '../../src/interfaces';
import { IMonthlyInspection } from 'contracts';
import { random } from 'lodash';

export const mockBaseJob = {
  jobId: 'test',
  companyId: 'WW',
  deviceKey: '**********',
  branchId: 'test',
  modelKey: 'test|test|test',
  createdBy: 'test',
  createdAt: new Date(),
  checkList: mockCheckList,
  modelIdentifiers: mockModelMasters.modelIdentifiers,
  modelTemplate: mockModelMasters,
  modelMaster: mockModelMasters,
  checkListValues: mockCheckListValues,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
  branch: {
    branchId: 'branchId',
    companyId: 'companyId',
    title: 'title',
  } as BranchEntity,
  shopUserKey: 'test',
  transformModelKey: () => {},
  isUpdatedToEstimated: () => true,
  status: JobStatus.IDENTITY_VERIFIED,
  suggestedPrice: 10000,
  isConfirmPrice: false,
  vendorType: 'MASS',
} as JobEntity;

export const mockCustomerInfo = {
  type: CustomerInfoType.IDENTITY_VERIFICATION,
  photo: mockBase64Jpg,
  identificationNumber: '1123231232213',
  thaiName: {
    title: 'นาย',
    firstName: 'สมชาย',
    middleName: '',
    lastName: 'ใจดี',
  },
  engName: {
    title: 'Mr.',
    firstName: 'Somchai',
    middleName: '',
    lastName: 'JaiDee',
  },
  address: {
    subdistrict: { code: '', name: 'สาย' },
    moo: '',
    district: { code: '', name: 'สายไหม' },
    trok: '',
    province: { code: '', name: 'กรุงเทพ' },
    road: ' ถ.สายไหม',
    houseNumber: '123/456',
    soi: '',
  },
  mobileNumber: '0123456789',
  birthDate: '12/12/1990',
  issueDate: '12/12/1990',
  expireDate: '99/99/9999',
};

export const mockJob = {
  jobId: 'test',
  companyId: 'WW',
  deviceKey: '**********',
  branchId: 'test',
  modelKey: 'test|test|test',
  createdBy: 'test',
  createdAt: new Date(),
  checkList: mockCheckList,
  modelIdentifiers: mockModelMasters.modelIdentifiers,
  modelTemplate: mockModelMasters,
  modelMaster: mockModelMasters,
  checkListValues: mockCheckListValues,
  depositContractLink: '/nemo-media/mockLink',
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
  branch: {
    branchId: 'branchId',
    companyId: 'companyId',
    title: 'title',
  } as BranchEntity,
  shopUserKey: 'test',
  transformModelKey: () => {},
  isUpdatedToEstimated: () => true,
  status: JobStatus.IDENTITY_VERIFIED,
  suggestedPrice: 10000,
  isConfirmPrice: false,
  vendorType: 'MASS',
  contract: {
    companyId: 'mockCompanyId',
    company: {
      companyId: 'mockCompanyId',
      title: 'mockTitle',
      logoUrl: 'mockLogoUrl',
    } as CompanyEntity,
    contractId: 'mockContractId',
    jobId: 'mockJobId',
    contractLink: 'mockContractLink',
    transactionLink: 'mockTransactionLink',
    customerInfo: mockCustomerInfo,
    customerKey: 'encrypted',
    customerRef: 'encrypted',
    consentList: [],
    consentListValue: [],
    createdAt: new Date(),
    createdBy: 'mockCreatedBy',
    job: mockBaseJob,
    status: 'draft',
  } as ContractEntity,
};

export const mockGetJobsRequest = {
  myJobs: {
    request: {
      query: { myJob: 'true', isCheckVendorType: 'true' },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: `r.adminUserKey = 'test-user-key' AND r.vendorType = 'MASS'`,
  },
  myQCJobs: {
    request: {
      query: { myQCJob: 'true', isCheckVendorType: 'true' },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: `r.qcBy = 'test-user-key' AND r.vendorType = 'MASS'`,
  },
  partialReceive: {
    request: { query: { partialReceived: 'true', isCheckVendorType: 'true' } },
    result: [
      `r.deliveryOrderId is not null`,
      `r.status = '${JobStatus.PURCHASED}'`,
      `deliveryOrder.status = '${DeliveryOrderStatus.PARTIAL_RECEIVED}'`,
      `r.vendorType = 'MASS'`,
    ].join(' AND '),
  },
  statusOne: {
    request: { query: { status: ['status1'], isCheckVendorType: 'true' } },
    result: `r.status IN ('status1') AND r.vendorType = 'MASS'`,
  },
  statusMoreThanOne: {
    request: {
      query: { status: ['status1', 'status2'], isCheckVendorType: 'true' },
    },
    result: `r.status IN ('status1','status2') AND r.vendorType = 'MASS'`,
  },
  shippingStatusOne: {
    request: {
      query: { shippingStatus: ['ship-status1'], isCheckVendorType: 'true' },
    },
    result: `r.shippingStatus IN ('ship-status1') AND r.vendorType = 'MASS'`,
  },
  shippingStatusMoreThanOne: {
    request: {
      query: {
        shippingStatus: ['ship-status1', 'ship-status2'],
        isCheckVendorType: 'true',
      },
    },
    result: `r.shippingStatus IN ('ship-status1','ship-status2') AND r.vendorType = 'MASS'`,
  },
  qcStatusOne: {
    request: { query: { qcStatus: ['fix'], isCheckVendorType: 'true' } },
    result: `r.qcStatus IN ('fix') AND r.vendorType = 'MASS'`,
  },
  qcStatusMoreThanOne: {
    request: {
      query: { qcStatus: ['fix', 'repair'], isCheckVendorType: 'true' },
    },
    result: `r.qcStatus IN ('fix','repair') AND r.vendorType = 'MASS'`,
  },
  others: {
    request: {
      query: {
        jobId: 'job-id',
        deliveryOrderId: 'doId',
        deviceKey: 'device-key',
        brand: 'iPhone',
        model: '12',
        rom: '256GB',
        branch: 'branch',
        isCheckVendorType: 'true',
      },
    },
    result: [
      `r.jobId ILIKE '%job-id%'`,
      `r.deliveryOrderId ILIKE '%doId%'`,
      `r.deviceKey ILIKE '%device-key%'`,
      `r.modelIdentifiers ->> 'brand' = 'iPhone'`,
      `r.modelIdentifiers ->> 'model' = '12'`,
      `r.modelIdentifiers ->> 'rom' = '256GB'`,
      `r.branchId = 'branch'`,
      `r.vendorType = 'MASS'`,
    ].join(' AND '),
  },
  all: {
    request: {
      query: {
        jobId: 'job-id',
        deliveryOrderId: 'doId',
        deviceKey: 'device-key',
        brand: 'iPhone',
        model: '12',
        rom: '256GB',
        branch: 'branch',
        status: ['status1', 'status2'],
        shippingStatus: ['ship-status1', 'ship-status2'],
        myJob: 'true',
        partialReceived: 'true',
        isCheckVendorType: 'true',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: [
      `r.jobId ILIKE '%job-id%'`,
      `r.deliveryOrderId ILIKE '%doId%'`,
      `r.deviceKey ILIKE '%device-key%'`,
      `r.modelIdentifiers ->> 'brand' = 'iPhone'`,
      `r.modelIdentifiers ->> 'model' = '12'`,
      `r.modelIdentifiers ->> 'rom' = '256GB'`,
      `r.branchId = 'branch'`,
      `r.status IN ('status1','status2')`,
      `r.shippingStatus IN ('ship-status1','ship-status2')`,
      `r.adminUserKey = 'test-user-key'`,
      [
        `r.deliveryOrderId is not null`,
        `r.status = '${JobStatus.PURCHASED}'`,
        `deliveryOrder.status = '${DeliveryOrderStatus.PARTIAL_RECEIVED}'`,
      ].join(' AND '),
      `r.vendorType = 'MASS'`,
    ].join(' AND '),
  },

  ReceiveByJobSearchJobIdOrDeviceKey: {
    request: {
      query: {
        status: [JobStatus.RECEIVED, JobStatus.PURCHASED],
        searchJobIdOrDeviceKey: 'searchField',
        isCheckVendorType: 'true',
      },
    },
    result: [
      `r.status IN ('${JobStatus.RECEIVED}','${JobStatus.PURCHASED}')`,
      `(r.jobId = 'searchField' or r.deviceKey = 'searchField')`,
      `r.vendorType = 'MASS'`,
    ].join(' AND '),
  },
  myRepairJob: {
    request: {
      query: {
        myRepairJob: 'true',
        jobIdOrDeviceKeyLike: 'jobIdTest',
        isCheckVendorType: 'true',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: [
      `(r.jobId ILIKE '%jobIdTest%' or r.deviceKey ILIKE '%jobIdTest%')`,
      `r.repairedBy =  'test-user-key'`,
      `r.vendorType = 'MASS'`,
    ].join(' AND '),
  },
  isRepaired: {
    request: {
      query: {
        isRepaired: 'true',
        jobIdOrDeviceKeyLike: 'jobIdTest',
        isCheckVendorType: 'true',
      },
    },
    result: [
      `(r.jobId ILIKE '%jobIdTest%' or r.deviceKey ILIKE '%jobIdTest%')`,
      `r.repairListValue is not null`,
      `r.vendorType = 'MASS'`,
    ].join(' AND '),
  },
  isNotRepaired: {
    request: {
      query: {
        isRepaired: 'false',
        jobIdOrDeviceKeyLike: 'jobIdTest',
        isCheckVendorType: 'true',
      },
    },
    result: [
      `(r.jobId ILIKE '%jobIdTest%' or r.deviceKey ILIKE '%jobIdTest%')`,
      `r.repairListValue is null`,
      `r.vendorType = 'MASS'`,
    ].join(' AND '),
  },
  myInspectionJob: {
    request: {
      query: { myInspectionJob: 'true' },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: `r.inspectedBy = 'test-user-key' and r.assignInspectAt is not null`,
  },
  repairAll: {
    request: {
      query: { repairAll: 'true' },
    },
    result: `((r.status = '${JobStatus.QC_COMPLETED}' AND r.qcStatus IN ('fix', 'refurbish')) OR (r.status = '${JobStatus.INSPECTION_FAILED}'))`,
  },
  minPrice: {
    request: {
      query: {
        minPrice: '10000',
        jobIdOrDeviceKeyLike: 'jobIdTest',
        isCheckVendorType: 'true',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: [
      `(r.jobId ILIKE '%jobIdTest%' or r.deviceKey ILIKE '%jobIdTest%')`,
      `r.suggested_price >= 10000`,
      `r.vendorType = 'MASS'`,
    ].join(' AND '),
  },
  maxPrice: {
    request: {
      query: {
        maxPrice: '20000',
        jobIdOrDeviceKeyLike: 'jobIdTest',
        isCheckVendorType: 'true',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: [
      `(r.jobId ILIKE '%jobIdTest%' or r.deviceKey ILIKE '%jobIdTest%')`,
      `r.suggested_price <= 20000`,
      `r.vendorType = 'MASS'`,
    ].join(' AND '),
  },
  minUpdatedDate: {
    request: {
      query: {
        minUpdatedDate: '2024-04-05T00:00:00.000Z',
        jobIdOrDeviceKeyLike: 'jobIdTest',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: [
      `(r.jobId ILIKE '%jobIdTest%' or r.deviceKey ILIKE '%jobIdTest%')`,
      `date_trunc('day', r.updated_at at time zone 'Asia/Bangkok') >= '2024-04-05'`,
    ].join(' AND '),
  },
  maxUpdatedDate: {
    request: {
      query: {
        maxUpdatedDate: '2024-04-05T00:00:00.000Z',
        jobIdOrDeviceKeyLike: 'jobIdTest',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: [
      `(r.jobId ILIKE '%jobIdTest%' or r.deviceKey ILIKE '%jobIdTest%')`,
      `date_trunc('day', r.updated_at at time zone 'Asia/Bangkok') <= '2024-04-05'`,
    ].join(' AND '),
  },
  minMaxUpdatedDate: {
    request: {
      query: {
        minUpdatedDate: '2024-04-03T00:00:00.000Z',
        maxUpdatedDate: '2024-04-05T00:00:00.000Z',
        jobIdOrDeviceKeyLike: 'jobIdTest',
        isCheckVendorType: 'true',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: [
      `(r.jobId ILIKE '%jobIdTest%' or r.deviceKey ILIKE '%jobIdTest%')`,
      `date_trunc('day', r.updated_at at time zone 'Asia/Bangkok') >= '2024-04-03'`,
      `date_trunc('day', r.updated_at at time zone 'Asia/Bangkok') <= '2024-04-05'`,
      `r.vendorType = 'MASS'`,
    ].join(' AND '),
  },
  wrongFormatUpdatedDate: {
    request: {
      query: {
        maxUpdatedDate: '2024-04-05',
        jobIdOrDeviceKeyLike: 'jobIdTest',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
  },
  wrongFormatPriceString: {
    request: {
      query: {
        minPrice: '1000 Baht',
        jobIdOrDeviceKeyLike: 'jobIdTest',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
  },
  wrongFormatPriceDecimal: {
    request: {
      query: {
        minPrice: '1000.00',
        jobIdOrDeviceKeyLike: 'jobIdTest',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
  },
  wrongFormatPriceZeroLead: {
    request: {
      query: {
        minPrice: '0100',
        jobIdOrDeviceKeyLike: 'jobIdTest',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
  },
  overTimeSpanUpdatedDate: {
    request: {
      query: {
        maxUpdatedDate: '2025-05-05T00:00:00.000Z',
        minUpdatedDate: '2024-04-05T00:00:00.000Z',
        jobIdOrDeviceKeyLike: 'jobIdTest',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: [
      `(r.jobId ILIKE '%jobIdTest%' or r.deviceKey ILIKE '%jobIdTest%')`,
      `date_trunc('day', r.updated_at at time zone 'Asia/Bangkok') <= '2024-04-05'`,
    ].join(' AND '),
  },
  minOverMaxUpdatedDate: {
    request: {
      query: {
        maxUpdatedDate: '2024-04-05T00:00:00.000Z',
        minUpdatedDate: '2024-05-05T00:00:00.000Z',
        jobIdOrDeviceKeyLike: 'jobIdTest',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: [
      `(r.jobId ILIKE '%jobIdTest%' or r.deviceKey ILIKE '%jobIdTest%')`,
      `date_trunc('day', r.updated_at at time zone 'Asia/Bangkok') <= '2024-04-05'`,
    ].join(' AND '),
  },
  isConfirmPriceFalse: {
    request: {
      query: {
        isConfirmPrice: 'false',
        isCheckVendorType: 'true',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: `r.isConfirmPrice = false AND r.vendorType = 'MASS'`,
  },
  isConfirmPriceTrue: {
    request: {
      query: {
        isConfirmPrice: 'true',
        isCheckVendorType: 'true',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: `r.isConfirmPrice = true AND r.vendorType = 'MASS'`,
  },
  isIncompleteAO: {
    request: {
      query: {
        isIncompleteAO: 'true',
        isCheckVendorType: 'true',
      },
      withUserContext: {
        name: 'test-name',
        userKey: 'test-user-key',
        companyId: 'ww',
      },
    },
    result: `allocationOrder.status IN ('${AllocationOrderStatus.PARTIAL_RECEIVED}', '${AllocationOrderStatus.REJECT_BY_SHOP}') AND r.aoShippingStatus IN ('${AOShippingStatus.SHIPPED}', '${AOShippingStatus.LOST}') AND r.vendorType = 'MASS'`,
  },
};

export const mockPurchasedJob = {
  jobId: 'test',
  companyId: 'WW',
  deviceKey: '**********',
  branchId: 'test',
  modelKey: 'test|test|test',
  createdBy: 'test',
  createdAt: new Date(),
  modelIdentifiers: mockModelMasters.modelIdentifiers,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
  branch: {
    branchId: 'branchId',
    companyId: 'companyId',
    title: 'title',
  } as BranchEntity,
  shopUserKey: 'test',
  status: JobStatus.PURCHASED,
  deliveryOrderId: 'test-do',
  deliveryOrder: {
    deliveryOrderId: 'test-do',
    companyId: 'company',
    branchId: 'branchId',
    quantity: 10,
    status: DeliveryOrderStatus.IN_TRANSIT,
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
  } as DeliveryOrderEntity,
};

export const mockReceivedJob = {
  jobId: 'test',
  companyId: 'WW',
  deviceKey: '**********',
  branchId: 'test',
  modelKey: 'test|test|test',
  createdBy: 'test',
  createdAt: new Date(),
  modelIdentifiers: mockModelMasters.modelIdentifiers,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
  branch: {
    branchId: 'branchId',
    companyId: 'companyId',
    title: 'title',
  } as BranchEntity,
  shopUserKey: 'test',
  status: JobStatus.RECEIVED,
  deliveryOrderId: 'test-do',
  deliveryOrder: {
    deliveryOrderId: 'test-do',
    companyId: 'company',
    branchId: 'branchId',
    quantity: 10,
    status: DeliveryOrderStatus.IN_TRANSIT,
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
  } as DeliveryOrderEntity,
};

export const mockQCCompletedJob = {
  jobId: 'test-qc',
  companyId: 'WW',
  deviceKey: '**********',
  branchId: 'test',
  modelKey: 'test|test|test',
  createdBy: 'test',
  createdAt: new Date(),
  modelIdentifiers: mockModelMasters.modelIdentifiers,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
  branch: {
    branchId: 'branchId',
    companyId: 'companyId',
    title: 'title',
  } as BranchEntity,
  shopUserKey: 'test',
  status: JobStatus.QC_COMPLETED,
  qcStatus: QCStatus.FIX,
  qcAt: new Date(),
  qcBy: 'test',
  deliveryOrderId: 'test-do',
  deliveryOrder: {
    deliveryOrderId: 'test-do',
    companyId: 'company',
    branchId: 'branchId',
    quantity: 10,
    status: DeliveryOrderStatus.IN_TRANSIT,
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
  } as DeliveryOrderEntity,
};

export const mockInTransitDO = [
  {
    createdAt: new Date(),
    updatedAt: new Date(),
    deliveryOrderId: 'test-do',
    companyId: 'WW',
    branchId: '80000430',
    quantity: 1,
    status: '20_IN_TRANSIT',
    senderUserKey: 'test',
    senderMobileNumber: '0999999999',
    shopUserKey: 'test',
    jobs: [mockJob],
    branch: {
      branchId: 'branchId',
      companyId: 'companyId',
      title: 'title',
    } as BranchEntity,
    shopUser: mockUserShopEntity,
    senderUser: mockUserShopEntity,
    senderUserCompanyId: 'WW',
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
  },
] as DeliveryOrderEntity[];

export const mockInTransitDOPartial = [
  {
    createdAt: new Date(),
    updatedAt: new Date(),
    deliveryOrderId: 'test-do',
    companyId: 'WW',
    branchId: '80000430',
    quantity: 10,
    status: '20_IN_TRANSIT',
    senderUserKey: 'test',
    senderMobileNumber: '0999999999',
    shopUserKey: 'test',
    jobs: [mockJob],
    branch: {
      branchId: 'branchId',
      companyId: 'companyId',
      title: 'title',
    } as BranchEntity,
    shopUser: mockUserShopEntity,
    senderUser: mockUserShopEntity,
    senderUserCompanyId: 'WW',
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
    transporterName: 'Test User',
    transporterMobileNumber: '0999999999',
  },
] as DeliveryOrderEntity[];

// --- RepairAssignedJob+ status with assign user for 65+ use
export const mockRepairedUser = {
  company: 'WW',
  userKey: 'test-repaired-user-key',
  name: 'test-repaired-user-name',
} as WithUserContext;

export const mockRepairAssignedJob = {
  ...mockQCCompletedJob,
  status: JobStatus.REPAIR_ASSIGNED,
  qcStatus: QCStatus.FIX,
  repairedBy: mockRepairedUser.userKey,
  estimatedGrade: 'A',
};

const getMockMonthlyStarter = (): Omit<
  IMonthlyInspection,
  'date' | 'allProduct'
> => {
  return {
    aGradeProduct: random(0, 100),
    otherGradeProduct: random(0, 100),
    scrapProduct: random(0, 100),
    completeStatus: random(0, 1) === 1,
  };
};

// --- monthlyInspection
export const getGenerateMockMonthly = (startDate: string, endDate: string) => {
  const oneDay = 1000 * 60 * 60 * 24;
  const start = new Date(startDate).getTime();
  const end = new Date(endDate).getTime();
  const mockDataByDate: any[] = [];
  const mockResult: IMonthlyInspection[] = [];
  for (let time = start; time <= end; time += oneDay) {
    const { aGradeProduct, otherGradeProduct, scrapProduct, completeStatus } =
      getMockMonthlyStarter();
    const date = new Date(time);
    const allProduct = aGradeProduct + otherGradeProduct + scrapProduct;

    const mock: IMonthlyInspection = {
      date,
      allProduct,
      aGradeProduct,
      otherGradeProduct,
      scrapProduct,
      completeStatus,
    };
    const mockData: any = {
      date,
      all_product: allProduct.toString(),
      a_grade_product: aGradeProduct.toString(),
      other_grade_product: otherGradeProduct.toString(),
      scrap_product: scrapProduct.toString(),
      not_complete_product: !completeStatus,
    };
    mockResult.push(mock);
    mockDataByDate.push(mockData);
  }
  return { mockResult, mockDataByDate };
};

export const mockInspectionJob = [
  {
    jobId: '00001',
    companyId: 'WW',
    deviceKey: '**********',
    branchId: 'test',
    modelKey: 'test|test|test',
    createdBy: 'test',
    createdAt: new Date(),
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    modelIdentifiers: mockModelMasters.modelIdentifiers,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
    branch: {
      branchId: 'branchId',
      companyId: 'companyId',
      title: 'title',
    } as BranchEntity,
    shopUserKey: 'test',
    status: JobStatus.INSPECTION_AUTO_COMPLETED,
    qcStatus: QCStatus.FIX,
    qcAt: new Date(),
    qcBy: 'test',
    deliveryOrderId: 'test-do',
    deliveryOrder: {
      deliveryOrderId: 'test-do',
      companyId: 'company',
      branchId: 'branchId',
      quantity: 10,
      status: DeliveryOrderStatus.IN_TRANSIT,
      updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    } as DeliveryOrderEntity,
    isConfirmPrice: true,
  },
  {
    jobId: '00002',
    companyId: 'WW',
    deviceKey: '**********',
    branchId: 'test',
    modelKey: 'test|test|test',
    createdBy: 'test',
    createdAt: new Date(),
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    modelIdentifiers: mockModelMasters.modelIdentifiers,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
    branch: {
      branchId: 'branchId',
      companyId: 'companyId',
      title: 'title',
    } as BranchEntity,
    shopUserKey: 'test',
    status: JobStatus.INSPECTION_COMPLETED,
    qcStatus: QCStatus.FIX,
    qcAt: new Date(),
    qcBy: 'test',
    deliveryOrderId: 'test-do',
    deliveryOrder: {
      deliveryOrderId: 'test-do',
      companyId: 'company',
      branchId: 'branchId',
      quantity: 10,
      status: DeliveryOrderStatus.IN_TRANSIT,
      updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    } as DeliveryOrderEntity,
    isConfirmPrice: true,
  },
] as JobEntity[];

export const mockInspectionJobNotConfirmed = [
  {
    ...mockInspectionJob[0],
    isConfirmPrice: false,
  },
  {
    ...mockInspectionJob[1],
    isConfirmPrice: false,
  },
] as JobEntity[];

export const mockInspectionJobWithUpdate = {
  jobId: '00001',
  companyId: 'WW',
  deviceKey: '**********',
  branchId: 'test',
  modelKey: 'test|test|test',
  createdBy: 'test',
  createdAt: new Date(),
  updatedAt: new Date('2099-02-14T00:00:00.000Z'),
  modelIdentifiers: mockModelMasters.modelIdentifiers,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
  branch: {
    branchId: 'branchId',
    companyId: 'companyId',
    title: 'title',
  } as BranchEntity,
  shopUserKey: 'test',
  status: JobStatus.INSPECTION_COMPLETED,
  qcStatus: QCStatus.FIX,
  qcAt: new Date(),
  qcBy: 'test',
  deliveryOrderId: 'test-do',
  deliveryOrder: {
    deliveryOrderId: 'test-do',
    companyId: 'company',
    branchId: 'branchId',
    quantity: 10,
    status: DeliveryOrderStatus.IN_TRANSIT,
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
  } as DeliveryOrderEntity,
  isConfirmPrice: true,
} as JobEntity;

export const mockInspectionJobNoUpdate = {
  jobId: '00003',
  companyId: 'WW',
  deviceKey: '**********',
  branchId: 'test',
  modelKey: 'test|test|test',
  createdBy: 'test',
  createdAt: new Date(),
  modelIdentifiers: mockModelMasters.modelIdentifiers,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
  branch: {
    branchId: 'branchId',
    companyId: 'companyId',
    title: 'title',
  } as BranchEntity,
  shopUserKey: 'test',
  status: JobStatus.INSPECTION_COMPLETED,
  qcStatus: QCStatus.FIX,
  qcAt: new Date(),
  qcBy: 'test',
  deliveryOrderId: 'test-do',
  deliveryOrder: {
    deliveryOrderId: 'test-do',
    companyId: 'company',
    branchId: 'branchId',
    quantity: 10,
    status: DeliveryOrderStatus.IN_TRANSIT,
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
  } as DeliveryOrderEntity,
  isConfirmPrice: true,
} as JobEntity;

export const mockProductJobWithConfirmPrice = [
  {
    jobId: '00001',
    companyId: 'WW',
    deviceKey: '**********',
    branchId: 'test',
    modelKey: 'test|test|test',
    createdBy: 'test',
    createdAt: new Date(),
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    modelIdentifiers: mockModelMasters.modelIdentifiers,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
    branch: {
      branchId: 'branchId',
      companyId: 'companyId',
      title: 'title',
    } as BranchEntity,
    shopUserKey: 'test',
    status: JobStatus.INSPECTION_AUTO_COMPLETED,
    qcStatus: QCStatus.FIX,
    qcAt: new Date(),
    qcBy: 'test',
    deliveryOrderId: 'test-do',
    isConfirmPrice: false,
    deliveryOrder: {
      deliveryOrderId: 'test-do',
      companyId: 'company',
      branchId: 'branchId',
      quantity: 10,
      status: DeliveryOrderStatus.IN_TRANSIT,
      updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    } as DeliveryOrderEntity,
  },
  {
    jobId: '00002',
    companyId: 'WW',
    deviceKey: '**********',
    branchId: 'test',
    modelKey: 'test|test|test',
    createdBy: 'test',
    createdAt: new Date(),
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    modelIdentifiers: mockModelMasters.modelIdentifiers,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
    branch: {
      branchId: 'branchId',
      companyId: 'companyId',
      title: 'title',
    } as BranchEntity,
    shopUserKey: 'test',
    status: JobStatus.INSPECTION_COMPLETED,
    qcStatus: QCStatus.FIX,
    qcAt: new Date(),
    qcBy: 'test',
    deliveryOrderId: 'test-do',
    isConfirmPrice: true,
    deliveryOrder: {
      deliveryOrderId: 'test-do',
      companyId: 'company',
      branchId: 'branchId',
      quantity: 10,
      status: DeliveryOrderStatus.IN_TRANSIT,
      updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    } as DeliveryOrderEntity,
  },
] as JobEntity[];

export const mockJobAOConfirm = [
  {
    jobId: '00001',
    companyId: 'WW',
    deviceKey: '**********',
    branchId: 'test',
    modelKey: 'test|test|test',
    createdBy: 'test',
    createdAt: new Date(),
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    modelIdentifiers: mockModelMasters.modelIdentifiers,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
    branch: {
      branchId: 'branchId',
      companyId: 'companyId',
      title: 'title',
    } as BranchEntity,
    shopUserKey: 'test',
    status: JobStatus.AO_CREATED,
    qcStatus: QCStatus.FIX,
    qcAt: new Date(),
    qcBy: 'test',
    isConfirmPrice: false,
    currentGrade: 'A',
    costPrice: 1000,
    wholeSalePrice: 2000,
    retailPrice: 2500,
    wholeSaleMargin: 1,
    retailMargin: 2,
    marginWholeSaleBaht: 3000,
    marginRetailBaht: 3500,
    aoShippingStatus: AOShippingStatus.SHIPPED,
  },
  {
    jobId: '00002',
    companyId: 'WW',
    deviceKey: '**********',
    branchId: 'test',
    modelKey: 'test|test|test',
    createdBy: 'test',
    createdAt: new Date(),
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    modelIdentifiers: mockModelMasters.modelIdentifiers,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
    branch: {
      branchId: 'branchId',
      companyId: 'companyId',
      title: 'title',
    } as BranchEntity,
    shopUserKey: 'test',
    status: JobStatus.AO_CREATED,
    qcStatus: QCStatus.FIX,
    qcAt: new Date(),
    qcBy: 'test',
    isConfirmPrice: true,
    currentGrade: 'B',
    costPrice: 1000,
    wholeSalePrice: 2000,
    retailPrice: 2500,
    wholeSaleMargin: 1,
    retailMargin: 2,
    marginWholeSaleBaht: 3000,
    marginRetailBaht: 3500,
    aoShippingStatus: AOShippingStatus.SHIPPED,
  },
  {
    jobId: '00003',
    companyId: 'WW',
    deviceKey: '**********',
    branchId: 'test',
    modelKey: 'test|test|test',
    createdBy: 'test',
    createdAt: new Date(),
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    modelIdentifiers: mockModelMasters.modelIdentifiers,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
    branch: {
      branchId: 'branchId',
      companyId: 'companyId',
      title: 'title',
    } as BranchEntity,
    shopUserKey: 'test',
    status: JobStatus.AO_CREATED,
    qcStatus: QCStatus.SCRAP,
    qcAt: new Date(),
    qcBy: 'test',
    isConfirmPrice: true,
    currentGrade: null,
    costPrice: 1000,
    wholeSalePrice: 2000,
    retailPrice: 2500,
    wholeSaleMargin: 1,
    retailMargin: 2,
    marginWholeSaleBaht: 3000,
    marginRetailBaht: 3500,
    aoShippingStatus: AOShippingStatus.SHIPPED,
  },
] as JobEntity[];

export const mockJobAOConfirmPartial = [
  {
    jobId: '00001',
    companyId: 'WW',
    deviceKey: '**********',
    branchId: 'test',
    modelKey: 'test|test|test',
    createdBy: 'test',
    createdAt: new Date(),
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    modelIdentifiers: mockModelMasters.modelIdentifiers,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
    branch: {
      branchId: 'branchId',
      companyId: 'companyId',
      title: 'title',
    } as BranchEntity,
    shopUserKey: 'test',
    status: JobStatus.AO_CREATED,
    qcStatus: QCStatus.FIX,
    qcAt: new Date(),
    qcBy: 'test',
    isConfirmPrice: false,
    currentGrade: 'A',
    costPrice: 1000,
    wholeSalePrice: 2000,
    retailPrice: 2500,
    wholeSaleMargin: 1,
    retailMargin: 2,
    marginWholeSaleBaht: 3000,
    marginRetailBaht: 3500,
    aoShippingStatus: AOShippingStatus.SHIPPED,
  },
  {
    jobId: '00002',
    companyId: 'WW',
    deviceKey: '**********',
    branchId: 'test',
    modelKey: 'test|test|test',
    createdBy: 'test',
    createdAt: new Date(),
    updatedAt: new Date('2024-02-14T00:00:00.000Z'),
    modelIdentifiers: mockModelMasters.modelIdentifiers,
    company: {
      companyId: 'companyId',
      title: 'title',
      logoUrl: 'logoUrl',
    } as CompanyEntity,
    branch: {
      branchId: 'branchId',
      companyId: 'companyId',
      title: 'title',
    } as BranchEntity,
    shopUserKey: 'test',
    status: JobStatus.AO_CREATED,
    qcStatus: QCStatus.FIX,
    qcAt: new Date(),
    qcBy: 'test',
    isConfirmPrice: true,
    currentGrade: 'B',
    costPrice: 1000,
    wholeSalePrice: 2000,
    retailPrice: 2500,
    wholeSaleMargin: 1,
    retailMargin: 2,
    marginWholeSaleBaht: 3000,
    marginRetailBaht: 3500,
    aoShippingStatus: AOShippingStatus.SHIPPED,
  },
] as JobEntity[];

export let mockJobMediUrl = {
  jobId: '00001',
  companyId: 'WW',
  deviceKey: '**********',
  branchId: 'test',
  modelKey: 'test|test|test',
  createdBy: 'test',
  createdAt: new Date(),
  updatedAt: new Date('2024-02-14T00:00:00.000Z'),
  modelIdentifiers: mockModelMasters.modelIdentifiers,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
  branch: {
    branchId: 'branchId',
    companyId: 'companyId',
    title: 'title',
  } as BranchEntity,
  shopUserKey: 'test',
  status: JobStatus.QUOTE_REQUESTED,
  modelTemplate: mockModelMasters,
  checkList: mockCheckList,
  isConfirmPrice: false,
  modelMaster: mockModelMasters,
  isUpdatedToEstimated: () => true,
  vendorType: 'MASS',
  checkListValues: {
    media: {
      product_information: {
        dead_pixel: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
        dead_pixel2: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
      },
      additional_product_information: {
        test1: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
      },
    },
    product_information: {
      device_color: 'black',
      battery_health: 'below_80_percent',
      screen_display: 'normal',
      country_of_purchase: 'th',
      additional_accessories: 'complete',
      icloud_or_google_account: 'can_sign_out',
    },
    additional_product_information: { device_condition: 'minor_marks' },
    product_additional_information: {
      additional_information: 'automate test model and function master',
    },
  },
};

export const mockJobCampaignSelect = {
  jobId: '00001',
  companyId: 'WW',
  deviceKey: '**********',
  branchId: 'test',
  modelKey: 'test|test|test',
  createdBy: 'userTest1',
  createdAt: new Date(),
  updatedAt: new Date('2024-02-14T00:00:00.000Z'),
  modelIdentifiers: mockModelMasters.modelIdentifiers,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
  branch: {
    branchId: 'branchId',
    companyId: 'companyId',
    title: 'title',
  } as BranchEntity,
  shopUserKey: 'test',
  status: JobStatus.IDENTITY_VERIFIED,
  modelTemplate: mockModelMasters,
  checkList: mockCheckList,
  isConfirmPrice: false,
  modelMaster: mockModelMasters,
  isUpdatedToEstimated: () => true,
  vendorType: 'MASS',
  checkListValues: {
    media: {
      product_information: {
        dead_pixel: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
        dead_pixel2: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
      },
      additional_product_information: {
        test1: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
      },
    },
    product_information: {
      device_color: 'black',
      battery_health: 'below_80_percent',
      screen_display: 'normal',
      country_of_purchase: 'th',
      additional_accessories: 'complete',
      icloud_or_google_account: 'can_sign_out',
    },
    additional_product_information: { device_condition: 'minor_marks' },
    product_additional_information: {
      additional_information: 'automate test model and function master',
    },
  },
};

export const mockJobCampaignSelect2 = {
  jobId: '00001',
  companyId: 'WW',
  deviceKey: '**********',
  branchId: 'test',
  modelKey: 'test|test|test',
  createdBy: 'userTest1',
  createdAt: new Date(),
  updatedAt: new Date('2024-02-14T00:00:00.000Z'),
  modelIdentifiers: mockModelMasters.modelIdentifiers,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
  branch: {
    branchId: 'branchId',
    companyId: 'companyId',
    title: 'title',
  } as BranchEntity,
  shopUserKey: 'test',
  status: JobStatus.IDENTITY_VERIFIED,
  modelTemplate: mockModelMasters,
  checkList: mockCheckList,
  isConfirmPrice: false,
  modelMaster: mockModelMasters,
  isUpdatedToEstimated: () => true,
  vendorType: 'MASS',
  checkListValues: {
    media: {
      product_information: {
        dead_pixel: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
        dead_pixel2: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
      },
      additional_product_information: {
        test1: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
      },
    },
    product_information: {
      device_color: 'black',
      battery_health: 'below_80_percent',
      screen_display: 'normal',
      country_of_purchase: 'th',
      additional_accessories: 'complete',
      icloud_or_google_account: 'can_sign_out',
    },
    additional_product_information: { device_condition: 'minor_marks' },
    product_additional_information: {
      additional_information: 'automate test model and function master',
    },
  },
};

export const mockJobCampaignSelectInvalidUser = {
  jobId: '00001',
  companyId: 'WW',
  deviceKey: '**********',
  branchId: 'test',
  modelKey: 'test|test|test',
  createdBy: 'ERROR',
  createdAt: new Date(),
  updatedAt: new Date('2024-02-14T00:00:00.000Z'),
  modelIdentifiers: mockModelMasters.modelIdentifiers,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
  branch: {
    branchId: 'branchId',
    companyId: 'companyId',
    title: 'title',
  } as BranchEntity,
  shopUserKey: 'test',
  status: JobStatus.IDENTITY_VERIFIED,
  modelTemplate: mockModelMasters,
  checkList: mockCheckList,
  isConfirmPrice: false,
  modelMaster: mockModelMasters,
  isUpdatedToEstimated: () => true,
  vendorType: 'MASS',
  checkListValues: {
    media: {
      product_information: {
        dead_pixel: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
        dead_pixel2: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
      },
      additional_product_information: {
        test1: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
      },
    },
    product_information: {
      device_color: 'black',
      battery_health: 'below_80_percent',
      screen_display: 'normal',
      country_of_purchase: 'th',
      additional_accessories: 'complete',
      icloud_or_google_account: 'can_sign_out',
    },
    additional_product_information: { device_condition: 'minor_marks' },
    product_additional_information: {
      additional_information: 'automate test model and function master',
    },
  },
};

export const mockJobCampaignSelectInvalidStatus = {
  jobId: '00001',
  companyId: 'WW',
  deviceKey: '**********',
  branchId: 'test',
  modelKey: 'test|test|test',
  createdBy: 'userTest1',
  createdAt: new Date(),
  updatedAt: new Date('2024-02-14T00:00:00.000Z'),
  modelIdentifiers: mockModelMasters.modelIdentifiers,
  company: {
    companyId: 'companyId',
    title: 'title',
    logoUrl: 'logoUrl',
  } as CompanyEntity,
  branch: {
    branchId: 'branchId',
    companyId: 'companyId',
    title: 'title',
  } as BranchEntity,
  shopUserKey: 'test',
  status: JobStatus.QUOTE_REQUESTED,
  modelTemplate: mockModelMasters,
  checkList: mockCheckList,
  isConfirmPrice: false,
  modelMaster: mockModelMasters,
  isUpdatedToEstimated: () => true,
  vendorType: 'MASS',
  checkListValues: {
    media: {
      product_information: {
        dead_pixel: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
        dead_pixel2: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
      },
      additional_product_information: {
        test1: [
          'company/WW/job/job-1/video/file_test_1.mp4',
          'company/WW/job/job-2/video/file_test_2.mp4',
        ],
      },
    },
    product_information: {
      device_color: 'black',
      battery_health: 'below_80_percent',
      screen_display: 'normal',
      country_of_purchase: 'th',
      additional_accessories: 'complete',
      icloud_or_google_account: 'can_sign_out',
    },
    additional_product_information: { device_condition: 'minor_marks' },
    product_additional_information: {
      additional_information: 'automate test model and function master',
    },
  },
};

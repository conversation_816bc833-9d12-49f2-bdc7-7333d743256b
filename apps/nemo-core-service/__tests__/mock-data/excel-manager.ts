import { CampaignEntity, ModelMasterEntity } from '../../src/entities';
import { ResponseOfReadExcelFileV2 } from 'src/excel/excel-manager.service';

export const mockSuccessColumn = [
  { header: 'PRODUCT_ID', key: 'PRODUCT_ID', width: 10 },
  { header: 'MATERIAL_ID', key: 'MATERIAL_ID', width: 10 },
  { header: 'SYSTEM_CODE', key: 'SYSTEM_CODE', width: 20 },
  { header: 'OWNER_NAME', key: 'OWNER_NAME', width: 15 },
  { header: 'BRAND', key: 'BRAND', width: 10 },
  { header: 'M<PERSON><PERSON>', key: 'M<PERSON><PERSON>', width: 10 },
  { header: 'CAPACITY', key: 'CAPACITY', width: 10 },
  { header: 'MODEL_PRICE', key: 'MODEL_PRICE', width: 10 },
  { header: 'RELEASE_YEAR', key: 'RELEASE_YEAR', width: 10 },
  { header: 'PERCENT_PURCHASE', key: 'PERCENT_PURCHASE', width: 10 },
  {
    header: 'MAXIMUM_PURCHASE_PRICE_GRADE_A',
    key: 'MAXIMUM_PURCHASE_PRICE_GRADE_A',
    width: 10,
  },
  {
    header: 'MAXIMUM_PURCHASE_PRICE_GRADE_B',
    key: 'MAXIMUM_PURCHASE_PRICE_GRADE_B',
    width: 10,
  },
  {
    header: 'MAXIMUM_PURCHASE_PRICE_GRADE_C',
    key: 'MAXIMUM_PURCHASE_PRICE_GRADE_C',
    width: 10,
  },
  {
    header: 'MAXIMUM_PURCHASE_PRICE_GRADE_D',
    key: 'MAXIMUM_PURCHASE_PRICE_GRADE_D',
    width: 10,
  },
];

export const mockSuccessColumnModelMaster = [
  { header: 'PRODUCT_ID', key: 'PRODUCT_ID', width: 10 },
  { header: 'MATERIAL_ID', key: 'MATERIAL_ID', width: 10 },
  { header: 'SYSTEM_CODE', key: 'SYSTEM_CODE', width: 20 },
  { header: 'OWNER_NAME', key: 'OWNER_NAME', width: 15 },
  { header: 'BRAND', key: 'BRAND', width: 10 },
  { header: 'MODEL', key: 'MODEL', width: 10 },
  { header: 'CAPACITY', key: 'CAPACITY', width: 10 },
  { header: 'MODEL_PRICE', key: 'MODEL_PRICE', width: 10 },
  { header: 'RELEASE_YEAR', key: 'RELEASE_YEAR', width: 10 },
  { header: 'PERCENT_PURCHASE', key: 'PERCENT_PURCHASE', width: 10 },
  {
    header: 'MAXIMUM_PURCHASE_PRICE_GRADE_A',
    key: 'MAXIMUM_PURCHASE_PRICE_GRADE_A',
    width: 10,
  },
  {
    header: 'MAXIMUM_PURCHASE_PRICE_GRADE_B',
    key: 'MAXIMUM_PURCHASE_PRICE_GRADE_B',
    width: 10,
  },
  {
    header: 'MAXIMUM_PURCHASE_PRICE_GRADE_C',
    key: 'MAXIMUM_PURCHASE_PRICE_GRADE_C',
    width: 10,
  },
  {
    header: 'MAXIMUM_PURCHASE_PRICE_GRADE_D',
    key: 'MAXIMUM_PURCHASE_PRICE_GRADE_D',
    width: 10,
  },
];

export const mockMissingColumn = [
  { header: 'PRODUCT_ID', key: 'PRODUCT_ID', width: 10 },
  {
    header: 'MAXIMUM_PURCHASE_PRICE_GRADE_A',
    key: 'MAXIMUM_PURCHASE_PRICE_GRADE_A',
    width: 10,
  },
  {
    header: 'MAXIMUM_PURCHASE_PRICE_GRADE_B',
    key: 'MAXIMUM_PURCHASE_PRICE_GRADE_B',
    width: 10,
  },
  {
    header: 'MAXIMUM_PURCHASE_PRICE_GRADE_C',
    key: 'MAXIMUM_PURCHASE_PRICE_GRADE_C',
    width: 10,
  },
  {
    header: 'MAXIMUM_PURCHASE_PRICE_GRADE_D',
    key: 'MAXIMUM_PURCHASE_PRICE_GRADE_D',
    width: 10,
  },
];

export const mockRowData = [
  [
    'ID',
    'MatID',
    'System code',
    'Owner name',
    'ยี่ห้อ',
    'รุ่น',
    'ความจุ',
    'ราคาเครื่อง',
    'ปี',
    '% รับซื้อ',
    'ราคารับซื้อสูงสุดของเกรด A',
    'ราคารับซื้อสูงสุดของเกรด B',
    'ราคารับซื้อสูงสุดของเกรด C',
    'ราคารับซื้อสูงสุดของเกรด D',
  ],
  [
    '001',
    '300000001',
    '',
    'WW',
    'iPhone',
    'iPhone 14',
    '128GB',
    32900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '002',
    '300000002',
    '',
    'WW',
    'iPhone',
    'iPhone 14',
    '256GB',
    36900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '003',
    '300000003',
    '',
    'WW',
    'iPhone',
    'iPhone 14',
    '512GB',
    45900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '004',
    '300000004',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Plus',
    '128GB',
    37900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '005',
    '300000005',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Plus',
    '256GB',
    41900.0,
    2023,
    null,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '006',
    '300000006',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Plus',
    '512GB',
    null,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '007',
    '300000007',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro',
    '128GB',
    41900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '008',
    '300000008',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro',
    '256GB',
    45900.0,
    2023,
    null,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '009',
    '300000009',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro',
    '512GB',
    null,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '010',
    '300000010',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro',
    '1TB',
    63900.0,
    2023,
    null,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '011',
    '300000011',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro Max',
    '128GB',
    44900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '012',
    '300000012',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro Max',
    '256GB',
    null,
    null,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '013',
    '300000013',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro Max',
    '512GB',
    57900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '014',
    '300000014',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro Max',
    '1TB',
    null,
    null,
    null,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
];

export const mockRowDataModalMaster = [
  [
    'ID',
    'MatID',
    'System code',
    'Owner name',
    'ยี่ห้อ',
    'รุ่น',
    'ความจุ',
    'Image URL',
    'ราคาเครื่อง',
    'ปี',
    '% รับซื้อ',
    'ราคารับซื้อสูงสุดของเกรด A',
    'ราคารับซื้อสูงสุดของเกรด B',
    'ราคารับซื้อสูงสุดของเกรด C',
    'ราคารับซื้อสูงสุดของเกรด D',
  ],
  [
    '001',
    '300000001',
    '',
    'WW',
    'iPhone',
    'iPhone 14',
    '128GB',
    32900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '002',
    '300000002',
    '',
    'WW',
    'iPhone',
    'iPhone 14',
    '256GB',
    36900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '003',
    '300000003',
    '',
    'WW',
    'iPhone',
    'iPhone 14',
    '512GB',
    45900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '004',
    '300000004',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Plus',
    '128GB',
    37900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '005',
    '300000005',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Plus',
    '256GB',
    41900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '006',
    '300000006',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Plus',
    '512GB',
    41900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '007',
    '300000007',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro',
    '128GB',
    41900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '008',
    '300000008',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro',
    '256GB',
    45900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '009',
    '300000009',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro',
    '512GB',
    60,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '010',
    '300000010',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro',
    '1TB',
    63900.0,
    2023,
    null,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '011',
    '300000011',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro Max',
    '128GB',
    44900.0,
    2023,
    null,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '012',
    '300000012',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro Max',
    '256GB',
    63900,
    2023,
    null,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '013',
    '300000013',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro Max',
    '512GB',
    57900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '014',
    '300000014',
    '',
    'WW',
    'iPhone',
    'iPhone 14 Pro Max',
    '1TB',
    null,
    null,
    null,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
  [
    '015',
    '300000015',
    '',
    'WW',
    'iPhone',
    'iPhone 15',
    '128GB',
    32900.0,
    2023,
    60,
    20000.0,
    15000.0,
    10000.0,
    8000.0,
  ],
];

export const expectedSuccessResult = [
  {
    modelKey: '001',
    matCode: '300000001',
    brand: 'iPhone',
    model: 'iPhone 14',
    rom: '128GB',
    referencePrice: '32900',
    modelYear: '2023',
    purchasedRatio: 60,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '002',
    matCode: '300000002',
    brand: 'iPhone',
    model: 'iPhone 14',
    rom: '256GB',
    referencePrice: '36900',
    modelYear: '2023',
    purchasedRatio: 60,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '003',
    matCode: '300000003',
    brand: 'iPhone',
    model: 'iPhone 14',
    rom: '512GB',
    referencePrice: '45900',
    modelYear: '2023',
    purchasedRatio: 60,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '004',
    matCode: '300000004',
    brand: 'iPhone',
    model: 'iPhone 14 Plus',
    rom: '128GB',
    referencePrice: '37900',
    modelYear: '2023',
    purchasedRatio: 60,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '005',
    matCode: '300000005',
    brand: 'iPhone',
    model: 'iPhone 14 Plus',
    rom: '256GB',
    referencePrice: '41900',
    modelYear: '2023',
    purchasedRatio: null,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '006',
    matCode: '300000006',
    brand: 'iPhone',
    model: 'iPhone 14 Plus',
    rom: '512GB',
    referencePrice: null,
    modelYear: '2023',
    purchasedRatio: 60,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '007',
    matCode: '300000007',
    brand: 'iPhone',
    model: 'iPhone 14 Pro',
    rom: '128GB',
    referencePrice: '41900',
    modelYear: '2023',
    purchasedRatio: 60,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '008',
    matCode: '300000008',
    brand: 'iPhone',
    model: 'iPhone 14 Pro',
    rom: '256GB',
    referencePrice: '45900',
    modelYear: '2023',
    purchasedRatio: null,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '009',
    matCode: '300000009',
    brand: 'iPhone',
    model: 'iPhone 14 Pro',
    rom: '512GB',
    referencePrice: null,
    modelYear: '2023',
    purchasedRatio: 60,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '010',
    matCode: '300000010',
    brand: 'iPhone',
    model: 'iPhone 14 Pro',
    rom: '1TB',
    referencePrice: '63900',
    modelYear: '2023',
    purchasedRatio: null,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '011',
    matCode: '300000011',
    brand: 'iPhone',
    model: 'iPhone 14 Pro Max',
    rom: '128GB',
    referencePrice: '44900',
    modelYear: '2023',
    purchasedRatio: 60,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '012',
    matCode: '300000012',
    brand: 'iPhone',
    model: 'iPhone 14 Pro Max',
    rom: '256GB',
    referencePrice: null,
    modelYear: null,
    purchasedRatio: 60,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '013',
    matCode: '300000013',
    brand: 'iPhone',
    model: 'iPhone 14 Pro Max',
    rom: '512GB',
    referencePrice: '57900',
    modelYear: '2023',
    purchasedRatio: 60,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
  {
    modelKey: '014',
    matCode: '300000014',
    brand: 'iPhone',
    model: 'iPhone 14 Pro Max',
    rom: '1TB',
    referencePrice: null,
    modelYear: null,
    purchasedRatio: null,
    gradeA: '20000',
    gradeB: '15000',
    gradeC: '10000',
    gradeD: '8000',
  },
];

export const mockEmployeeColumn = [
  { header: 'EMP_ID', key: 'EMP_ID', width: 10 },
  { header: 'EMP_NAME', key: 'EMP_NAME', width: 10 },
  { header: 'EMP_SNAME', key: 'EMP_SNAME', width: 10 },
  { header: 'NAME_ENG', key: 'NAME_ENG', width: 10 },
  { header: 'SNAME_ENG', key: 'SNAME_ENG', width: 10 },
  { header: 'EMP_TYPE2', key: 'EMP_TYPE2', width: 10 },
  { header: 'SHOP_CODE', key: 'SHOP_CODE', width: 10 },
  { header: 'USER_TYPE', key: 'USER_TYPE', width: 10 },
  { header: 'SITE', key: 'SITE', width: 10 },
  { header: 'EMAIL', key: 'EMAIL', width: 10 },
];

export const mockEmployeeColumnNew = [
  { header: 'EMP_ID', key: 'EMP_ID', width: 10 },
  { header: 'EMP_NAME', key: 'EMP_NAME', width: 10 },
  { header: 'EMP_SNAME', key: 'EMP_SNAME', width: 10 },
  { header: 'NAME_ENG', key: 'NAME_ENG', width: 10 },
  { header: 'SNAME_ENG', key: 'SNAME_ENG', width: 10 },
  { header: 'EMP_TYPE', key: 'EMP_TYPE', width: 10 },
  { header: 'SHOP_CODE', key: 'SHOP_CODE', width: 10 },
  { header: 'USER_TYPE', key: 'USER_TYPE', width: 10 },
  { header: 'SITE', key: 'SITE', width: 10 },
  { header: 'EMAIL', key: 'EMAIL', width: 10 },
];

export const mockEmployeeRowData = [
  [
    'EMP_ID',
    'EMP_NAME',
    'EMP_SNAME',
    'NAME_ENG',
    'SNAME_ENG',
    'EMP_TYPE2',
    'SHOP_CODE',
    'USER_TYPE',
    'SITE',
    'EMAIL',
  ],
  [
    '00000001',
    'ทดสอบ',
    'ระบบ',
    'Test',
    'System',
    'MANAGER',
    '10000001',
    'WW',
    'FRONTSHOP',
    '<EMAIL>',
  ],
  [
    '00000002',
    'กก',
    'ผผ',
    'AA',
    'II',
    'MANAGER',
    '10000002',
    'WW',
    'FRONTSHOP',
    '<EMAIL>',
  ],
  [
    '00000003',
    'ขข',
    'นน',
    'BB',
    'JJ',
    'MANAGER',
    '10000003',
    'WW',
    'FRONTSHOP',
    '<EMAIL>',
  ],
  [
    '00000004',
    'คค',
    'ยย',
    'CC',
    'KK',
    'MANAGER',
    '10000002',
    'WW',
    'FRONTSHOP',
    '<EMAIL>',
  ],
  [
    '00000005',
    'งง',
    'รร',
    'DD',
    'LL',
    'MANAGER',
    '10000003',
    'WW',
    'FRONTSHOP',
    '<EMAIL>',
  ],
  [
    '00000006',
    'จจ',
    'พพ',
    'EE',
    'MM',
    'SALE',
    '10000001',
    'WW',
    'FRONTSHOP',
    '<EMAIL>',
  ],
  [
    '00000007',
    'ทท',
    'ดด',
    'FF',
    'NN',
    'SALE',
    '10000002',
    'WW',
    'FRONTSHOP',
    '<EMAIL>',
  ],
  [
    '00000008',
    'บบ',
    'มม',
    'GG',
    'OO',
    'QC',
    'ADMIN_BRANCH',
    'WW',
    'CMS',
    '<EMAIL>',
  ],
  [
    '00000009',
    'ปป',
    'สส',
    'HH',
    'PP',
    'QC',
    'ADMIN_BRANCH',
    'WW',
    'CMS',
    '<EMAIL>',
  ],
  [
    '00000009',
    'ปป',
    'สส',
    'HH',
    'PP',
    'QC',
    'ADMIN_BRANCH',
    'WW',
    'CMS',
    '<EMAIL>',
  ],
];

export const mockShopColumn = [
  { header: 'CODE', key: 'CODE', width: 10 },
  { header: 'SHOP_TH1', key: 'SHOP_TH1', width: 10 },
];

export const mockShopRowData = [
  ['CODE', 'SHOP_TH1'],
  ['10000001', 'TEST_SHOP1'],
  ['10000002', 'TEST_SHOP2'],
  ['10000003', 'TEST_SHOP3'],
];

// mock for read excel v2
export const mockHeaderExcelV2 = [
  { header: 'PRODUCT_ID', key: 'PRODUCT_ID', width: 10 },
  {
    header: 'remobie_check_list.bluetooth.check',
    key: 'remobie_check_list.bluetooth.check',
    width: 10,
  },
  {
    header: 'remobie_check_list.bluetooth=functional',
    key: 'remobie_check_list.bluetooth=functional',
    width: 10,
  },
  {
    header: 'remobie_check_list.bluetooth=non_functional',
    key: 'remobie_check_list.bluetooth=non_functional',
    width: 10,
  },
  {
    header: 'remobie_check_list.bluetooth=skip',
    key: 'remobie_check_list.bluetooth=skip',
    width: 10,
  },
];

export const mockHeaderExcelV2BadRequired = [
  { header: 'PRODUCT_ID', key: 'PRODUCT_ID', width: 10 },
];

export const mockRowExcelV2 = [
  [
    'ID',
    'เช็คBluetooth',
    'Bluetoothใช้ได้',
    'Bluetoothใช้ไม่ได้',
    'ข้ามBluetooth',
  ],
  ['apple|iphone 11pro|256gb', true, 1000, 0, 0],
];

export const mockCheckListForExcel = [
  {
    id: '249HD1VNCOCFD1U03',
    createdAt: '2024-09-17T06:31:48.239Z',
    updatedAt: '2024-09-17T06:31:48.239Z',
    companyId: 'WW',
    functionKey: 'bluetooth',
    functionSection: 'remobie_check_list',
    isRequired: true,
    checklistType: 'MODULE',
    checklistNameTh: 'Bluetooth',
    checklistNameEn: 'Bluetooth',
    checklistDescriptionTh: null,
    checklistDescriptionEn: null,
    tooltipTh: null,
    tooltipEn: null,
    placeholderTh: null,
    placeholderEn: null,
    errorTextTh: null,
    errorTextEn: null,
    iconImageUrl:
      'https://ww-remobile.axonstech.com/asset/company/WW/icons/bluetooth.svg',
    moduleCode: 'BLUETOOTH',
    questionType: null,
    questionChoices: null,
    isIncludeVideo: false,
    tooltip: null,
    popup: null,
  },
  {
    id: '249HD1VNCOCFD1U04',
    createdAt: '2024-09-17T06:31:48.239Z',
    updatedAt: '2024-09-17T06:31:48.239Z',
    companyId: 'WW',
    functionKey: 'wifi',
    functionSection: 'remobie_check_list',
    isRequired: false,
    checklistType: 'MODULE',
    checklistNameTh: 'wifi',
    checklistNameEn: 'wifi',
    checklistDescriptionTh: null,
    checklistDescriptionEn: null,
    tooltipTh: null,
    tooltipEn: null,
    placeholderTh: null,
    placeholderEn: null,
    errorTextTh: null,
    errorTextEn: null,
    iconImageUrl:
      'https://ww-remobile.axonstech.com/asset/company/WW/icons/wifi.svg',
    moduleCode: 'WIFI',
    questionType: null,
    questionChoices: null,
    isIncludeVideo: false,
    tooltip: null,
    popup: null,
  },
];

export const expectedSuccessResultOfExcelv2: ResponseOfReadExcelFileV2 = {
  willBeSaveData: [
    {
      PRODUCT_ID: 'apple|iphone 11pro|256gb',
      'remobie_check_list.bluetooth=functional': 1000,
      'remobie_check_list.bluetooth=non_functional': 0,
      'remobie_check_list.bluetooth=skip': 0,
    },
  ],
  willBeRemoveRowData: [
    {
      keys: ['remobie_check_list.wifi'],
      productID: 'apple|iphone 11pro|256gb',
    },
  ],
};

// mock for generate excel v2
export const mockmasterModels = [
  {
    createdAt: '2024-09-17T06:31:48.239Z',
    updatedAt: '2024-09-17T06:31:48.239Z',
    companyId: 'WW',
    modelKey: 'apple-test',
    modelIdentifiers: { rom: '256gb', brand: 'apple', model: 'iphone12' },
    templateId: 'v1',
    modelMasterGrades: [],
    modelExportDetails: null,
    createdBy: 'system',
    updatedBy: 'system',
    matCode: null,
    systemCode: null,
    modelImageUrl: null,
    referencePrice: 0,
    modelYear: null,
    purchasedRatio: 0,
    insuranceCost: 0,
    matCodeSale: null,
    averageRetailCost: null,
    averageWholeSaleCost: null,
  },
];
export const mockmasterQuestions = [
  ...mockCheckListForExcel,
  {
    id: '249HD1VNMCU3ZZPD1',
    createdAt: '2024-09-17T06:31:48.239Z',
    updatedAt: '2024-09-17T06:31:48.239Z',
    companyId: 'WW',
    functionKey: 'logout_icloud',
    functionSection: 'product_information',
    isRequired: false,
    checklistType: 'QUESTION',
    checklistNameTh: 'สามารถออกจาก iCloud ',
    checklistNameEn: 'iCloud logout',
    checklistDescriptionTh: '',
    checklistDescriptionEn: '',
    tooltipTh: null,
    tooltipEn: null,
    placeholderTh: null,
    placeholderEn: null,
    errorTextTh: null,
    errorTextEn: null,
    iconImageUrl: '',
    moduleCode: null,
    questionType: 'SELECTION',
    questionChoices: [
      {
        id: 'pass',
        answerEn: 'Can',
        answerTh: 'ได้',
        iconImageUrl:
          'https://ww-remobile.axonstech.com/asset/company/WW/icons/calling-pass.png',
      },
      {
        id: 'fail',
        answerEn: 'Can not',
        answerTh: 'ไม่ได้',
        iconImageUrl:
          'https://ww-remobile.axonstech.com/asset/company/WW/icons/calling-fail.png',
      },
    ],
    isIncludeVideo: false,
    tooltip: null,
    popup: null,
  },
];
export const mockAnswer = [
  {
    modelKey: 'apple-test',
    'remobie_check_list.bluetooth=functional': '0',
    'remobie_check_list.bluetooth=non_functional': '-1000',
    'product_information.logout_icloud=pass': '0',
    'product_information.logout_icloud=fail': '1000',
  },
];

export const mockSuccessColumnCampaignSummary = [
  { header: 'CAMPAIGN_CODE', key: 'campaignCode' },
  { header: 'CAMPAIGN_NAME', key: 'campaignName' },
  { header: 'DESCRIPTION', key: 'description' },
  { header: 'REMARK', key: 'remark' },
  { header: 'START_DATE', key: 'startDate' },
  { header: 'END_DATE', key: 'endDate' },
  { header: 'MAX_REDEMPTION_CODE', key: 'maxRedemtionCode' },
];

export const mockNewRowDataCampaignSummary = [
  [
    'Campaign Code',
    'Campaign Name max 100 digit',
    'Description max 300 digit',
    'Remark (สำหรับใส่) max 100 digit',
    'Start date',
    'End date',
    'No of display',
  ],
  ['A001', 'NameA001', 'Desc001', 'Remark001', new Date(), new Date(), 10],
  ['A002', 'NameA002', 'Desc002', 'Remark002', new Date(), new Date(), 20],
  ['A003', 'NameA003', 'Desc003', 'Remark003', new Date(), new Date(), 30],
  ['A004', 'NameA004', 'Desc004', 'Remark004', new Date(), new Date(), 40],
  ['A005', 'NameA005', 'Desc005', 'Remark005', new Date(), new Date(), 50],
  ['B001', 'NameB001', 'Desc001', 'Remark001', new Date(), new Date(), 10],
];

export const mockSuccessColumnCampaignModel = [
  { header: 'CAMPAIGN_CODE', key: 'campaignCode' },
  { header: 'MODEL_KEY', key: 'modelKey' },
];

export const mockNewRowDataCampaignModel = [
  ['Campaign Code', 'Model_Key'],
  ['A001', 'apple|iphone 14 plus|128gb'],
  ['A001', 'apple|iphone 14 plus|256gb'],
  ['A002', 'apple|iphone 14 plus|128gb'],
  ['A002', 'apple|iphone 14 plus|256gb'],
];

export const mockCampaignData = [
  {
    companyId: 'WW',
    campaignCode: 'A001',
    campaignName: 'NameB001',
    description: 'Desc001',
    remark: 'Remark001',
    maxRedemptionCode: 10,
    startDate: new Date(),
    endDate: new Date(),
    createdBy: 'system',
    updatedBy: 'system',
  },
  {
    companyId: 'WW',
    campaignCode: 'A002',
    campaignName: 'NameB002',
    description: 'Desc002',
    remark: 'Remark002',
    maxRedemptionCode: 10,
    startDate: new Date(),
    endDate: new Date(),
    createdBy: 'system',
    updatedBy: 'system',
  },
] as CampaignEntity[];

export const mockModelMasters = [
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 14 plus|128gb',
    modelIdentifiers: {
      rom: '128GB',
      brand: 'Apple',
      model: 'iPhone 14 Plus',
    },
    templateId: 'v1',
    modelMasterGrades: [
      {
        grade: 'A',
        purchasePrice: '37900.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
      {
        grade: 'B',
        purchasePrice: '37900.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
      {
        grade: 'C',
        purchasePrice: '37900.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
      {
        grade: 'D',
        purchasePrice: '37900.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
    ],
    createdBy: 'system',
    updatedBy: 'system',
  },
  {
    companyId: 'WW',
    modelKey: 'apple|iphone 14 plus|256gb',
    modelIdentifiers: {
      rom: '256GB',
      brand: 'Apple',
      model: 'iPhone 14 Plus',
    },
    templateId: 'v1',
    modelMasterGrades: [
      {
        grade: 'A',
        purchasePrice: '37900.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
      {
        grade: 'B',
        purchasePrice: '37900.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
      {
        grade: 'C',
        purchasePrice: '37900.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
      {
        grade: 'D',
        purchasePrice: '37900.00',
        lastPurchasedOn: 'ISO8601',
        lastPurchasedPrice: 'Grade Info',
      },
    ],
    createdBy: 'system',
    updatedBy: 'system',
  },
] as ModelMasterEntity[];

export const mockSuccessColumnCampaignRedemptionCode = [
  { header: 'CAMPAIGN_CODE', key: 'campaignCode' },
  { header: 'REDEMPTION_CODE', key: 'redemptionCode' },
  { header: 'SUPPORTER', key: 'supporter' },
  { header: 'GRADE', key: 'grade' },
  { header: 'CODE_VALUE', key: 'value' },
  { header: 'CODE_ORDER', key: 'order' },
];

export const generateCampaignCodeExcelDataListMock = (total: number) => {
  const mock: any[] = [];

  // add second row for header
  mock.push({
    campaignCode: 'xxxx',
    redemptionCode: 'xxxx',
    supporter: 'xxxx',
    grade: 'A',
    value: 100,
    order: 1,
  });

  for (let i = 1; i <= total; i++) {
    mock.push({
      campaignCode: 'CODE' + i,
      redemptionCode: 'REDEEM' + i,
      supporter: 'TEST SUPPORT',
      grade: 'a,b,c,d',
      value: 100 + i,
      order: (i % 4) + 1,
    });
  }
  return mock;
};

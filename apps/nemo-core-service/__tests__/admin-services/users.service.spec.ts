import { getRepositoryToken } from '@nestjs/typeorm';
import { Test } from '@nestjs/testing';
import {
  BranchEntity,
  CompanyEntity,
  CompanyRoleEntity,
  UserEntity,
  RoleEntity,
  UserRoleBranchEntity,
  GeneralActivitiesEntity,
  UserVendorTypeMappingEntity,
} from '../../src/entities';
import { BaseExceptionModule } from '../../src/base-exceptions/base-exceptions.module';
import {
  AdminUsersService,
  userOption,
} from '../../src/admin/users/users.service';
import { ExcelManagerModule } from '../../src/excel/excel-manager.module';
import { EncryptDecryptModule } from '../../src/encrypt-decrypt-message/encrypt-decrypt.module';
import { WithUserContext } from '../../src/interfaces';
import { Repository } from 'typeorm';
import {
  mockCustomUserQueryResult,
  mockUserEntity,
  mockuserAfterLoadNew,
  mockuserAfterLoadResp,
  mockuserAfterLoadNew2,
  mockuserAfterLoadResp2,
} from '../mock-data/user';
import { GetMeResponse } from 'contracts';
import { BASE_EXCEPTIONS } from '../../src/config';
import { BaseExceptionService } from '../../src/exceptions';
import * as ExcelJS from 'exceljs';
import { Readable } from 'stream';
import {
  mockEmployeeColumnNew,
  mockEmployeeRowData,
} from '../mock-data/excel-manager';
import { CacheManagerService } from '../../src/cache-manager/cache-manager.service';

describe('AdminUsersService', () => {
  let usersService: AdminUsersService;

  let userRepository: Repository<UserEntity>;
  let branchRepository: Repository<BranchEntity>;
  let roleRepository: Repository<RoleEntity>;
  let companyRepository: Repository<CompanyEntity>;
  let userRoleBranchRepository: Repository<UserRoleBranchEntity>;
  let userVendorTypeMappingRepository: Repository<UserVendorTypeMappingEntity>;

  const createQueryBuilder = jest.fn(() => ({
    select: createQueryBuilder,
    addSelect: createQueryBuilder,
    orderBy: createQueryBuilder,
    offset: createQueryBuilder,
    limit: createQueryBuilder,
    insert: createQueryBuilder,
    into: createQueryBuilder,
    values: createQueryBuilder,
    orUpdate: createQueryBuilder,
    execute: createQueryBuilder,
    from: createQueryBuilder,
    where: createQueryBuilder,
    andWhere: createQueryBuilder,
    getRawMany: jest.fn(() => mockCustomUserQueryResult),
    getRawOne: jest.fn(() => {
      return { count: '1' };
    }),
  }));

  const mockUserContext: WithUserContext = {
    userKey: '<EMAIL>',
    company: 'WW',
    idToken: 'test1234',
  };

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        EncryptDecryptModule.register(),
        BaseExceptionModule.register(),
        ExcelManagerModule.register(userOption),
      ],
      providers: [
        AdminUsersService,
        {
          provide: CacheManagerService,
          useValue: {
            incrData: jest.fn(() => Promise.resolve(1)),
            getData: jest.fn(),
            setData: jest.fn(),
            removeBatchData: jest.fn(),
            removeData: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
            find: jest.fn().mockReturnThis(),
            count: jest.fn().mockReturnThis(),
            manager: {
              connection: {
                query: () => [{ count: '1' }],
                createQueryRunner: jest.fn().mockReturnThis(),
                connect: jest.fn().mockReturnThis(),
                startTransaction: jest.fn().mockReturnThis(),
                release: jest.fn().mockReturnThis(),
                rollbackTransaction: jest.fn().mockReturnThis(),
                commitTransaction: jest.fn().mockReturnThis(),
                manager: {
                  delete: jest.fn().mockReturnThis(),
                  save: jest.fn().mockReturnThis(),
                  update: jest.fn().mockReturnThis(),
                },
              },

              transaction: jest.fn() as jest.Mock,
            },
            createQueryBuilder: createQueryBuilder,
          },
        },
        {
          provide: getRepositoryToken(BranchEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            createQueryBuilder: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
            into: jest.fn().mockReturnThis(),
            values: jest.fn().mockReturnThis(),
            orUpdate: jest.fn().mockReturnThis(),
            execute: jest.fn().mockReturnThis(),
            manager: {
              transaction: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CompanyRoleEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            forEach: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(RoleEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(UserRoleBranchEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            manager: {
              connection: { query: () => [{ count: '1' }] },
              transaction: jest.fn() as jest.Mock,
              find: jest.fn().mockReturnThis(),
            },
          },
        },
        {
          provide: getRepositoryToken(GeneralActivitiesEntity),
          useValue: {
            find: jest.fn().mockReturnThis(),
            save: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(CompanyEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getRepositoryToken(UserVendorTypeMappingEntity),
          useValue: {
            findOne: jest.fn().mockReturnThis(),
          },
        },
      ],
    }).compile();

    usersService = module.get<AdminUsersService>(AdminUsersService);
    userRepository = module.get<Repository<UserEntity>>(
      getRepositoryToken(UserEntity),
    );
    branchRepository = module.get<Repository<BranchEntity>>(
      getRepositoryToken(BranchEntity),
    );
    userRoleBranchRepository = module.get<Repository<UserRoleBranchEntity>>(
      getRepositoryToken(UserRoleBranchEntity),
    );
    roleRepository = module.get<Repository<RoleEntity>>(
      getRepositoryToken(RoleEntity),
    );

    companyRepository = module.get<Repository<CompanyEntity>>(
      getRepositoryToken(CompanyEntity),
    );
    userVendorTypeMappingRepository = module.get<
      Repository<UserVendorTypeMappingEntity>
    >(getRepositoryToken(UserVendorTypeMappingEntity));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Get me', () => {
    const expected: GetMeResponse = {
      userKey: 'test',
      name: 'test',
      branchRoles: [
        {
          branchId: 'ADMIN',
          branchName: 'ADMIN',
          roles: [],
        },
      ],
      roleConfig: [],
      permissionGroupConfig: [],
      userType: 'WW',
      vendorType: 'MASS',
    };

    it('should response profile', async () => {
      jest
        .spyOn(userRepository, 'findOne')
        .mockResolvedValueOnce(mockUserEntity);
      jest.spyOn(userRoleBranchRepository, 'find').mockResolvedValueOnce([]);
      jest
        .spyOn(userRoleBranchRepository.manager, 'find')
        .mockResolvedValueOnce([]);
      jest
        .spyOn(userRoleBranchRepository.manager, 'find')
        .mockResolvedValueOnce([]);
      jest
        .spyOn(userVendorTypeMappingRepository, 'findOne')
        .mockResolvedValueOnce({ userType: 'WW', vendorType: 'MASS' } as any);

      const result = await usersService.getMe(mockUserContext);

      expect(result).toEqual(expected);
    });
    it('should response empty name profile', async () => {
      jest
        .spyOn(userRepository, 'findOne')
        .mockResolvedValueOnce({ ...mockUserEntity, name: '' });
      jest.spyOn(userRoleBranchRepository, 'find').mockResolvedValueOnce([]);
      jest
        .spyOn(userRoleBranchRepository.manager, 'find')
        .mockResolvedValueOnce([]);
      jest
        .spyOn(userRoleBranchRepository.manager, 'find')
        .mockResolvedValueOnce([]);
      jest
        .spyOn(userVendorTypeMappingRepository, 'findOne')
        .mockResolvedValueOnce({ userType: 'WW', vendorType: 'MASS' } as any);

      const result = await usersService.getMe(mockUserContext);

      expect(result).toEqual({ ...expected, name: '' });
    });

    it('should throw error not found', async () => {
      jest.spyOn(userRepository, 'findOne').mockReturnValueOnce(null as any);

      let error: any;

      try {
        await usersService.getMe(mockUserContext);
      } catch (err) {
        error = err;
      }

      expect(error).toBeInstanceOf(BaseExceptionService);

      expect(error.message).toEqual(BASE_EXCEPTIONS.NOT_FOUND_DATA.message);
    });
  });

  describe('Get user by id', () => {
    const expected = {
      userKey: 'testUserKey',
      name: 'ทดสอบ',
      lastname: 'ระบบ',
      nameEn: 'Test',
      lastnameEn: 'User',
      roles: {
        frontshop: [
          {
            roleId: 'ROLEID1',
            branch: {
              branchId: 'BRANCHID1',
              branchName: 'test branch',
            },
          },
        ],
        cms: [
          {
            roleId: 'ROLEID2',
          },
          {
            roleId: 'ROLEID3',
          },
        ],
      },
    };

    const expectedNoRole = {
      userKey: 'testUserKey',
      name: 'ทดสอบ',
      lastname: 'ระบบ',
      nameEn: 'Test',
      lastnameEn: 'User',
      roles: {
        frontshop: [],
        cms: [],
      },
    };

    const expectedInCompleteInfo = {
      userKey: 'testUserKey',
      name: 'ทดสอบ',
      lastname: '',
      nameEn: '',
      lastnameEn: '',
      roles: {
        frontshop: [],
        cms: [],
      },
    };

    const mockUser = {
      companyId: 'WW',
      userKey: 'testUserKey',
      name: 'ทดสอบ ระบบ',
      nameEng: 'Test User',
    } as UserEntity;

    const mockUserIncompleteInfo = {
      companyId: 'WW',
      userKey: 'testUserKey',
      name: 'ทดสอบ',
    } as UserEntity;
    const mockUserRoleBranch = [
      {
        companyId: 'WW',
        userKey: 'testUserKey',
        roleId: 'ROLEID1',
        branchId: 'BRANCHID1',
        role: {
          roleId: 'ROLEID1',
          companyId: 'WW',
          roleName: 'MANAGER',
          type: 'FRONTSHOP',
        } as RoleEntity,
        branch: {
          companyId: 'WW',
          branchId: 'BRANCHID1',
          title: 'test branch',
        } as BranchEntity,
        user: mockUser,
      },
      {
        companyId: 'WW',
        userKey: 'testUserKey',
        roleId: 'ROLEID2',
        branchId: 'BRANCHID2',
        role: {
          roleId: 'ROLEID2',
          companyId: 'WW',
          roleName: 'ADMIN1',
          type: 'CMS',
        } as RoleEntity,
        branch: {
          companyId: 'WW',
          branchId: 'BRANCHID2',
          title: 'test branch',
        } as BranchEntity,
        user: mockUser,
      },
      {
        companyId: 'WW',
        userKey: 'testUserKey',
        roleId: 'ROLEID3',
        branchId: 'BRANCHID3',
        role: {
          roleId: 'ROLEID3',
          companyId: 'WW',
          roleName: 'ADMIN',
          type: 'CMS',
        } as RoleEntity,
        branch: {
          companyId: 'WW',
          branchId: 'BRANCHID3',
          title: 'test branch',
        } as BranchEntity,
        user: mockUser,
      },
    ] as UserRoleBranchEntity[];

    it('should response user data full', async () => {
      jest.spyOn(userRepository, 'findOne').mockResolvedValueOnce(mockUser);
      jest
        .spyOn(userRoleBranchRepository, 'find')
        .mockResolvedValueOnce(mockUserRoleBranch);

      const result = await usersService.getUserById('testUserKey');
      expect(result).toEqual(expected);
    });

    it('should response user data (no role) ', async () => {
      jest.spyOn(userRepository, 'findOne').mockResolvedValueOnce(mockUser);
      jest.spyOn(userRoleBranchRepository, 'find').mockResolvedValueOnce([]);

      const result = await usersService.getUserById('testUserKey');
      expect(result).toEqual(expectedNoRole);
    });

    it('should response user data (incomplete personal info) ', async () => {
      jest
        .spyOn(userRepository, 'findOne')
        .mockResolvedValueOnce(mockUserIncompleteInfo);
      jest.spyOn(userRoleBranchRepository, 'find').mockResolvedValueOnce([]);

      const result = await usersService.getUserById('testUserKey');
      expect(result).toEqual(expectedInCompleteInfo);
    });

    it('should throw error not found', async () => {
      jest.spyOn(userRepository, 'findOne').mockResolvedValueOnce(null);

      try {
        await usersService.getUserById('testUserKey');
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
      }
    });
  });

  describe('Put users', () => {
    const mockBranch = [
      { branchId: '10000001', companyId: 'WW' },
      { branchId: '10000002', companyId: 'WW' },
      { branchId: '10000003', companyId: 'WW' },
      { branchId: '10000004', companyId: 'WW' },
      { branchId: 'ADMIN_BRANCH', companyId: 'WW' },
    ] as BranchEntity[];

    const mockCompany = {
      companyId: 'WW',
      title: 'TEST',
    } as CompanyEntity;

    const mockRole = [
      {
        companyId: 'WW',
        roleId: '0001',
        roleName: 'MANAGER',
        type: 'FRONTSHOP',
      },
      { companyId: 'WW', roleId: '0002', roleName: 'SALE', type: 'FRONTSHOP' },
      { companyId: 'WW', roleId: '0003', roleName: 'QC', type: 'CMS' },
    ] as RoleEntity[];

    it('should sucessfully save', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('REPORT_MANPOWER_WW');
      const employeeWorksheet = workbook.worksheets[0];
      employeeWorksheet.columns = mockEmployeeColumnNew;

      for (let i = 0; i < mockEmployeeRowData.length; i++) {
        employeeWorksheet.addRow(mockEmployeeRowData[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      jest.spyOn(branchRepository, 'find').mockResolvedValueOnce(mockBranch);
      jest
        .spyOn(companyRepository, 'findOne')
        .mockResolvedValueOnce(mockCompany);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce(mockRole);
      jest.spyOn(userRoleBranchRepository, 'find').mockResolvedValueOnce([]);

      const result = await usersService.saveEmployee(
        'WW',
        mockFile,
        mockUserContext,
      );
      expect(result).toBeNull();
    });

    it('should sucessfully save (with existing user role)', async () => {
      const userRoleBranch = {
        branchId: '10000001',
        companyId: 'WW',
        roleId: '0001',
        userKey: '00000001',
      } as UserRoleBranchEntity;

      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('REPORT_MANPOWER_WW');
      const employeeWorksheet = workbook.worksheets[0];
      employeeWorksheet.columns = mockEmployeeColumnNew;

      for (let i = 0; i < mockEmployeeRowData.length; i++) {
        employeeWorksheet.addRow(mockEmployeeRowData[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      jest.spyOn(branchRepository, 'find').mockResolvedValueOnce(mockBranch);
      jest
        .spyOn(companyRepository, 'findOne')
        .mockResolvedValueOnce(mockCompany);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce(mockRole);
      jest
        .spyOn(userRoleBranchRepository, 'find')
        .mockResolvedValueOnce([userRoleBranch]);

      const result = await usersService.saveEmployee(
        'WW',
        mockFile,
        mockUserContext,
      );
      expect(result).toBeNull();
    });

    it('should error save user', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('REPORT_MANPOWER_WW');
      const employeeWorksheet = workbook.worksheets[0];
      employeeWorksheet.columns = mockEmployeeColumnNew;

      for (let i = 0; i < mockEmployeeRowData.length; i++) {
        employeeWorksheet.addRow(mockEmployeeRowData[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      jest.spyOn(branchRepository, 'find').mockResolvedValueOnce(mockBranch);
      jest
        .spyOn(companyRepository, 'findOne')
        .mockResolvedValueOnce(mockCompany);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce(mockRole);
      jest.spyOn(userRoleBranchRepository, 'find').mockResolvedValueOnce([]);
      const mockEntityManager = {
        createQueryBuilder: jest.fn().mockReturnValue({
          insert: jest.fn().mockReturnThis(),
          into: jest.fn().mockReturnThis(),
          values: jest.fn().mockReturnThis(),
          orUpdate: jest.fn().mockReturnThis(),
          execute: jest.fn().mockRejectedValue(new Error('Mock Insert Error')),
        }),
      };

      (userRepository.manager.transaction as jest.Mock).mockImplementation(
        async (callback: any) => {
          await callback(mockEntityManager);
        },
      );

      try {
        await usersService.saveEmployee('WW', mockFile, mockUserContext);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }
    });

    it('should error role not exists', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('REPORT_MANPOWER_WW');
      const employeeWorksheet = workbook.worksheets[0];
      employeeWorksheet.columns = mockEmployeeColumnNew;

      for (let i = 0; i < mockEmployeeRowData.length; i++) {
        employeeWorksheet.addRow(mockEmployeeRowData[i]);
      }

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      jest.spyOn(branchRepository, 'find').mockResolvedValueOnce(mockBranch);
      jest
        .spyOn(companyRepository, 'findOne')
        .mockResolvedValueOnce(mockCompany);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce([]);

      try {
        await usersService.saveEmployee('WW', mockFile, mockUserContext);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.ROLE_NOT_EXISTS.code,
        );
      }
    });
    it('should error email invalid', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('REPORT_MANPOWER_WW');
      const employeeWorksheet = workbook.worksheets[0];
      employeeWorksheet.columns = mockEmployeeColumnNew;
      const userRoleBranch = {
        branchId: '10000001',
        companyId: 'WW',
        roleId: '0001',
        userKey: '00000001',
      } as UserRoleBranchEntity;
      const mockWrongEmail = [
        '00000001',
        'ทดสอบ',
        'ระบบ',
        'Test',
        'System',
        'MANAGER',
        '10000001',
        'WW',
        'FRONTSHOP',
        'abcde',
      ];

      employeeWorksheet.addRow(mockWrongEmail);
      employeeWorksheet.addRow(mockWrongEmail);

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      jest.spyOn(branchRepository, 'find').mockResolvedValueOnce(mockBranch);
      jest
        .spyOn(companyRepository, 'findOne')
        .mockResolvedValueOnce(mockCompany);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce(mockRole);
      jest
        .spyOn(userRoleBranchRepository, 'find')
        .mockResolvedValueOnce([userRoleBranch]);

      try {
        await usersService.saveEmployee('WW', mockFile, mockUserContext);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Invalid email pattern',
        );
      }
    });

    it('should error invalid site type', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('REPORT_MANPOWER_WW');
      const employeeWorksheet = workbook.worksheets[0];
      employeeWorksheet.columns = mockEmployeeColumnNew;

      const mockWrongSite = [
        '00000001',
        'ทดสอบ',
        'ระบบ',
        'Test',
        'System',
        'MANAGER',
        '10000001',
        'WW',
        'WRONG_SITE',
        '<EMAIL>',
      ];

      employeeWorksheet.addRow(mockWrongSite);
      employeeWorksheet.addRow(mockWrongSite);

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      jest.spyOn(branchRepository, 'find').mockResolvedValueOnce(mockBranch);
      jest
        .spyOn(companyRepository, 'findOne')
        .mockResolvedValueOnce(mockCompany);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce(mockRole);

      try {
        await usersService.saveEmployee('WW', mockFile, mockUserContext);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe('Invalid site type');
      }
    });

    it('should error role type not match with site type', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('REPORT_MANPOWER_WW');
      const employeeWorksheet = workbook.worksheets[0];
      employeeWorksheet.columns = mockEmployeeColumnNew;

      const mockWrongSite = [
        '00000001',
        'ทดสอบ',
        'ระบบ',
        'Test',
        'System',
        'QC',
        '10000001',
        'WW',
        'FRONTSHOP',
        '<EMAIL>',
      ];

      employeeWorksheet.addRow(mockWrongSite);
      employeeWorksheet.addRow(mockWrongSite);

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      jest.spyOn(branchRepository, 'find').mockResolvedValueOnce(mockBranch);
      jest
        .spyOn(companyRepository, 'findOne')
        .mockResolvedValueOnce(mockCompany);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce(mockRole);

      try {
        await usersService.saveEmployee('WW', mockFile, mockUserContext);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Role type not match with site type',
        );
      }
    });

    it('should error branch is required', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('REPORT_MANPOWER_WW');
      const employeeWorksheet = workbook.worksheets[0];
      employeeWorksheet.columns = mockEmployeeColumnNew;

      const mockWrongSite = [
        '00000001',
        'ทดสอบ',
        'ระบบ',
        'Test',
        'System',
        'MANAGER',
        '',
        'WW',
        'FRONTSHOP',
        '<EMAIL>',
      ];

      employeeWorksheet.addRow(mockWrongSite);
      employeeWorksheet.addRow(mockWrongSite);

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      jest.spyOn(branchRepository, 'find').mockResolvedValueOnce(mockBranch);
      jest
        .spyOn(companyRepository, 'findOne')
        .mockResolvedValueOnce(mockCompany);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce(mockRole);

      try {
        await usersService.saveEmployee('WW', mockFile, mockUserContext);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Branch ID is required, when site is Frontshop',
        );
      }
    });

    it('should error branch not exists', async () => {
      const workbook = new ExcelJS.Workbook();
      workbook.addWorksheet('REPORT_MANPOWER_WW');
      const employeeWorksheet = workbook.worksheets[0];
      employeeWorksheet.columns = mockEmployeeColumnNew;

      const mockWrongSite = [
        '00000001',
        'ทดสอบ',
        'ระบบ',
        'Test',
        'System',
        'MANAGER',
        '10000010',
        'WW',
        'FRONTSHOP',
        '<EMAIL>',
      ];

      employeeWorksheet.addRow(mockWrongSite);
      employeeWorksheet.addRow(mockWrongSite);

      const mockBuffer = (await workbook.xlsx.writeBuffer()) as Buffer;

      const mockFile: Express.Multer.File = {
        originalname: 'mockExcel.xlsx',
        mimetype:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        encoding: '7bit',
        buffer: mockBuffer,
        fieldname: 'test',
        size: 10,
        filename: 'mockExcel.xlsx',
        path: 'testPath',
        destination: 'destination',
        stream: new Readable(),
      };

      jest.spyOn(branchRepository, 'find').mockResolvedValueOnce(mockBranch);
      jest
        .spyOn(companyRepository, 'findOne')
        .mockResolvedValueOnce(mockCompany);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce(mockRole);

      try {
        await usersService.saveEmployee('WW', mockFile, mockUserContext);
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BRANCH_NOT_EXISTS.code,
        );
      }
    });
  });

  describe('After Load', () => {
    it('should return correctly', async () => {
      const result = await usersService.afterLoad(
        mockuserAfterLoadNew as any[],
      );
      expect(result).toEqual(mockuserAfterLoadResp);
    });
    it('should return correctly (userRoleBranch empty)', async () => {
      const result = await usersService.afterLoad(
        mockuserAfterLoadNew2 as any[],
      );
      expect(result).toEqual(mockuserAfterLoadResp2);
    });
  });

  describe('getUserList', () => {
    it('should return correctly', async () => {
      const query = {
        searchEmployee: '',
        role: '',
        branch: '',
        page: '1',
        pageSize: '10',
        pagination: 'true',
        orderBy: 'userKey asc',
      };
      jest
        .spyOn(userRepository, 'find')
        .mockResolvedValueOnce(mockuserAfterLoadNew as any[]);
      const result = await usersService.getUserList('WW', query);
      expect(result).toHaveProperty('items');
      expect(result).toHaveProperty('paginationResult');
    });
  });

  describe('Patch user by id', () => {
    const mockUser = {
      companyId: 'WW',
      userKey: 'testUserKey',
      name: 'ทดสอบ ระบบ',
      nameEng: 'Test User',
    } as UserEntity;

    const mockBranchEntity = [
      {
        companyId: 'WW',
        branchId: 'BRANCHID1',
        title: 'title#1',
      },
      {
        companyId: 'WW',
        branchId: 'BRANCHID2',
        title: 'title#2',
      },
    ] as BranchEntity[];

    const mockRoleEntity = [
      {
        roleId: 'ROLEID1',
        companyId: 'WW',
        roleName: 'MANAGER',
        type: 'FRONTSHOP',
      },
      {
        roleId: 'ROLEID2',
        companyId: 'WW',
        roleName: 'QC',
        type: 'CMS',
      },
      {
        roleId: 'ROLEID3',
        companyId: 'WW',
        roleName: 'SALE',
        type: 'FRONTSHOP',
      },
    ] as RoleEntity[];

    const mockUserContext: WithUserContext = {
      userKey: '<EMAIL>',
      company: 'WW',
      idToken: 'test1234',
    };

    it('should success update', async () => {
      const mockBody = {
        name: 'ทดสอบ ระบบ',
        nameEN: 'Test User',
        isActive: true,
        roles: [
          {
            roleId: 'ROLEID1',
            branchId: 'BRANCHID1',
          },
          {
            roleId: 'ROLEID2',
          },
        ],
        email: '<EMAIL>',
      } as any;
      jest.spyOn(userRepository, 'findOne').mockResolvedValueOnce(mockUser);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce(mockRoleEntity);
      jest
        .spyOn(branchRepository, 'find')
        .mockResolvedValueOnce(mockBranchEntity);

      const result = await usersService.updateUserById(
        'testUserKey',
        mockBody,
        mockUserContext,
      );
      expect(result).toBeNull();
    });

    it('should error user not found', async () => {
      jest.spyOn(userRepository, 'findOne').mockResolvedValueOnce(null);

      try {
        await usersService.updateUserById(
          'testUserKey',
          {} as any,
          mockUserContext,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.NOT_FOUND_DATA.code,
        );
        expect((error as BaseExceptionService).data).toBe('User not found');
      }
    });

    it('should error role not found', async () => {
      const mockBody = {
        name: 'ทดสอบ ระบบ',
        nameEN: 'Test User',
        isActive: true,
        roles: [
          {
            roleId: 'ROLEID1X',
            branchId: 'BRANCHID1',
          },
          {
            roleId: 'ROLEID2',
          },
        ],
        email: '<EMAIL>',
      } as any;
      jest.spyOn(userRepository, 'findOne').mockResolvedValueOnce(mockUser);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce(mockRoleEntity);
      jest
        .spyOn(branchRepository, 'find')
        .mockResolvedValueOnce(mockBranchEntity);

      try {
        await usersService.updateUserById(
          'testUserKey',
          mockBody,
          mockUserContext,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.ROLE_NOT_EXISTS.code,
        );
      }
    });

    it('should error branch is required when role type is frontshop', async () => {
      const mockBody = {
        name: 'ทดสอบ ระบบ',
        nameEN: 'Test User',
        isActive: true,
        roles: [
          {
            roleId: 'ROLEID1',
          },
          {
            roleId: 'ROLEID2',
          },
        ],
        email: '<EMAIL>',
      } as any;
      jest.spyOn(userRepository, 'findOne').mockResolvedValueOnce(mockUser);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce(mockRoleEntity);
      jest
        .spyOn(branchRepository, 'find')
        .mockResolvedValueOnce(mockBranchEntity);

      try {
        await usersService.updateUserById(
          'testUserKey',
          mockBody,
          mockUserContext,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BODY_PAYLOAD_INVALID.code,
        );
        expect((error as BaseExceptionService).data).toBe(
          'Branch ID is required, when role type is Frontshop',
        );
      }
    });

    it('should error branch not found', async () => {
      const mockBody = {
        name: 'ทดสอบ ระบบ',
        nameEN: 'Test User',
        isActive: true,
        roles: [
          {
            roleId: 'ROLEID1',
            branchId: 'BRANCHID10',
          },
          {
            roleId: 'ROLEID2',
          },
        ],
        email: '<EMAIL>',
      } as any;
      jest.spyOn(userRepository, 'findOne').mockResolvedValueOnce(mockUser);
      jest.spyOn(roleRepository, 'find').mockResolvedValueOnce(mockRoleEntity);
      jest
        .spyOn(branchRepository, 'find')
        .mockResolvedValueOnce(mockBranchEntity);

      try {
        await usersService.updateUserById(
          'testUserKey',
          mockBody,
          mockUserContext,
        );
        fail('Expected method to throw an error');
      } catch (error) {
        expect(error).toBeInstanceOf(BaseExceptionService);
        expect((error as BaseExceptionService).code).toBe(
          BASE_EXCEPTIONS.BRANCH_NOT_EXISTS.code,
        );
        expect((error as BaseExceptionService).data).toBe('Branch not found');
      }
    });
  });

  describe('upsertUser', () => {
    const mockUserContext: WithUserContext = {
      userKey: 'admin',
      company: 'WW',
      idToken: 'token',
    };
    const mockAllRoles = [
      {
        roleId: 'CMS1',
        type: 'CMS',
        companyId: 'WW',
        roleName: 'ADMIN',
        company: {},
        rolePermissions: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        roleId: 'FS1',
        type: 'FRONTSHOP',
        companyId: 'WW',
        roleName: 'SALE',
        company: {},
        rolePermissions: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
    const mockAllBranches = [
      {
        branchId: 'B1',
        companyId: 'WW',
        title: 'Branch 1',
        titleEn: 'Branch 1',
        branchType: 'Shop',
        createdAt: new Date(),
        updatedAt: new Date(),
        company: {},
      },
      {
        branchId: 'B2',
        companyId: 'WW',
        title: 'Branch 2',
        titleEn: 'Branch 2',
        branchType: 'Shop',
        createdAt: new Date(),
        updatedAt: new Date(),
        company: {},
      },
    ];
    const mockPutUserUpsertDto = {
      employeeId: 'EMP001',
      firstNameTh: 'สมชาย',
      lastNameTh: 'ใจดี',
      firstNameEn: 'Somchai',
      lastNameEn: 'Jaidee',
      email: '<EMAIL>',
      frontShopRoles: [{ roleId: 'FS1', branchId: 'B1' }],
      cmsAdminRoles: ['CMS1'],
    };
    function getMockQueryRunner() {
      return {
        connect: jest.fn(),
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        manager: {
          save: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
        },
      } as any;
    }
    it('should create a new user if not exists', async () => {
      jest
        .spyOn(userRepository.manager.connection, 'createQueryRunner')
        .mockReturnValue(getMockQueryRunner());
      jest.spyOn(userRepository, 'findOne').mockResolvedValueOnce(null);
      jest.spyOn(roleRepository, 'find').mockResolvedValue(mockAllRoles as any);
      jest
        .spyOn(branchRepository, 'find')
        .mockResolvedValue(mockAllBranches as any);
      const result = await usersService.upsertUser(
        mockPutUserUpsertDto as any,
        mockUserContext,
      );
      expect(result).toBeNull();
    });
    it('should update user if exists', async () => {
      jest
        .spyOn(userRepository.manager.connection, 'createQueryRunner')
        .mockReturnValue(getMockQueryRunner());
      jest
        .spyOn(userRepository, 'findOne')
        .mockResolvedValueOnce({ userKey: 'EMP001', companyId: 'WW' } as any);
      jest.spyOn(roleRepository, 'find').mockResolvedValue(mockAllRoles as any);
      jest
        .spyOn(branchRepository, 'find')
        .mockResolvedValue(mockAllBranches as any);
      const result = await usersService.upsertUser(
        mockPutUserUpsertDto as any,
        mockUserContext,
      );
      expect(result).toBeNull();
    });
    it('should throw error if role not exists', async () => {
      jest
        .spyOn(userRepository.manager.connection, 'createQueryRunner')
        .mockReturnValue(getMockQueryRunner());
      jest.spyOn(userRepository, 'findOne').mockResolvedValueOnce(null);
      jest.spyOn(roleRepository, 'find').mockResolvedValue([]);
      jest
        .spyOn(branchRepository, 'find')
        .mockResolvedValue(mockAllBranches as any);
      const input = {
        ...mockPutUserUpsertDto,
        frontShopRoles: [{ roleId: 'NOT_EXIST_ROLE', branchId: 'B1' }],
        cmsAdminRoles: [],
      };
      try {
        await usersService.upsertUser(input as any, mockUserContext);
        fail('Expected method to throw an error');
      } catch (error) {
        const err = error as BaseExceptionService;
        expect(err).toBeInstanceOf(BaseExceptionService);
        expect(err.code).toBe('110001'); // ROLE_NOT_EXISTS
        expect(err.message).toBe('Role not exists');
      }
    });
    it('should throw error if branch not exists', async () => {
      jest
        .spyOn(userRepository.manager.connection, 'createQueryRunner')
        .mockReturnValue(getMockQueryRunner());
      jest.spyOn(userRepository, 'findOne').mockResolvedValueOnce(null);
      jest.spyOn(roleRepository, 'find').mockResolvedValue(mockAllRoles as any);
      jest.spyOn(branchRepository, 'find').mockResolvedValue([]);
      const input = {
        ...mockPutUserUpsertDto,
        frontShopRoles: [{ roleId: 'FS1', branchId: 'NOT_EXIST_BRANCH' }],
        cmsAdminRoles: [],
      };
      try {
        await usersService.upsertUser(input as any, mockUserContext);
        fail('Expected method to throw an error');
      } catch (error) {
        const err = error as BaseExceptionService;
        expect(err).toBeInstanceOf(BaseExceptionService);
        expect(err.code).toBe('110002'); // BRANCH_NOT_EXISTS
        expect(err.message).toBe('Branch not exists');
      }
    });
  });
});

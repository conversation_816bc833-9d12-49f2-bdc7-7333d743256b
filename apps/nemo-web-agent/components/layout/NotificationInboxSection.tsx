import { FC, useCallback, useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { useTranslation } from '@/hooks'
import { onSnapshot, collection, getFirestore, limit, or, orderBy, query, where } from 'firebase/firestore'
import {
  notificationConfigByType,
  Notification as INotification,
  renderInboxIcon,
  renderInboxMessage,
} from '@/constants/notification'
import { useUserStore } from '@/stores/userStore'
import { displayTime, formatMillisecondsToDateTime, isTimestampInCurrentDate } from '@/utils'
import { Drawer } from 'ui'
import { usePersistStore } from '@/stores/persistStore'
import {
  askForPermissionAndCallNotification,
  checkPermissionAndCallNotification,
  ICallNotiProp,
  ITypeNoti,
  PermissionAction,
  PermissionMapCode,
} from 'utils'
import { ISystemModalType, useSystemStore } from '@/stores/systemStore'
import { InboxType } from 'contracts'
import { ShopMenuSlug } from 'ui/src/models'

interface NotificationInboxProps {
  companyId: string
  setHaveUnreadNotification: (status: boolean) => void
  openNotificationFromNavBar: boolean
  clearOpenNotificationFromNavBar: () => void
}

const NotificationInbox: FC<NotificationInboxProps> = ({
  companyId,
  setHaveUnreadNotification,
  openNotificationFromNavBar,
  clearOpenNotificationFromNavBar,
}) => {
  const { t } = useTranslation('common')
  const router = useRouter()

  // ----- Store ----- //
  const { user: currentUser } = useUserStore()
  const {
    showCommonModal,
    hideCommonModal,
    systemModalOpenType,
    setSystemModalOpenType,
    clearSystemModalOpenType,
    permissionConfig,
  } = useSystemStore()
  const { currentBranchId, lastOpenedInboxInMilliseconds, setLastOpenedInboxInMilliseconds, isRoleManager } =
    usePersistStore()

  // ----- useState ----- //
  const [isMount, setMount] = useState<boolean>(false)
  const [notiData, setNotiData] = useState<any>(null)
  const [showInbox, setShowInbox] = useState<boolean>(false)
  const [lastOpenInbox, setLastOpenInbox] = useState<number>(0)
  const [notifications, setNotifications] = useState<INotification[]>([])

  // --- life cycle ---//
  useEffect(() => {
    const userKey = currentUser?.userKey ?? ''
    if (userKey) {
      setLastOpenInbox(lastOpenedInboxInMilliseconds[userKey] ?? 0)
    }

    const subscribeInbox = subscribeInboxNotification()
    return () => {
      subscribeInbox?.()
    }
  }, [currentUser])

  useEffect(() => {
    if (showInbox) {
      const queryInbox = getQueryInbox(20)

      if (!queryInbox) return
      return onSnapshot(queryInbox, (querySnapshot: any) => {
        const documents: INotification[] = querySnapshot.docs.map((doc: any) => ({ ...doc.data() }) as INotification)

        setNotifications(documents)
      })
    }
  }, [showInbox])

  useEffect(() => {
    if (openNotificationFromNavBar) {
      openNotificationHandler(true)
      clearOpenNotificationFromNavBar()
    }
  }, [openNotificationFromNavBar])

  useEffect(() => {
    if (notiData !== null) {
      if (isMount) {
        const callNotiProp = getNotificationConfigFromNotiData(notiData)
        checkPermissionAndCallNotification(callNotiProp, type => {
          if ([ITypeNoti.granted, ITypeNoti.NoAccess].includes(type)) {
            setNotiData(null)
          } else if (type === ITypeNoti.AskForPermission) {
            setSystemModalOpenType(ISystemModalType.askForPermissionNotification)
          }
        })
      }
      setMount(true)
    }
  }, [notiData])

  useEffect(() => {
    if (systemModalOpenType === ISystemModalType.askForPermissionNotification) {
      const callNotiProp = getNotificationConfigFromNotiData(notiData)
      showCommonModal({
        type: 'warning',
        buttonType: 'single',
        onClose: onCloseCommonModalAskNotiPermission,
        onClickPositiveButton: () => {
          askForPermissionAndCallNotification(callNotiProp)
          onCloseCommonModalAskNotiPermission()
        },
        positiveButtonTxt: t('notification.ask-for-notification-permission-modal-button'),
        title: t('notification.ask-for-notification-permission-modal-title'),
        description: t('notification.ask-for-notification-permission-modal-description'),
        icon: 'ic-warning',
        iconCls: '!h-16 !w-16',
      })
    }
  }, [systemModalOpenType])

  // ----- function: notification function ----- //
  const openNotificationHandler = (isOpenAction: boolean) => {
    const timestamp = new Date().getTime()
    setLastOpenedInboxInMilliseconds(currentUser?.userKey ?? '', timestamp)
    setHaveUnreadNotification(false)
    setShowInbox(isOpenAction)
    if (!isOpenAction) {
      setLastOpenInbox(timestamp)
    }
  }

  const getQueryInbox = useCallback(
    (queryLimit: number) => {
      const userKey = currentUser?.userKey ?? ''
      if (!userKey || !currentBranchId || !permissionConfig) return null
      const queryPath = `company/${companyId}/inbox/${currentBranchId}/message`
      const inboxColRef = collection(getFirestore(), queryPath)
      const queryArray: any[] = []

      if (permissionConfig[PermissionMapCode.SHOP_JOB_MY]?.[PermissionAction.CREATE]) {
        queryArray.push(where('audience', '==', `${PermissionMapCode.SHOP_JOB_MY}_CREATE:${userKey}`))
      }
      if (permissionConfig[PermissionMapCode.SHOP_OTHERS_OCR_CONFIRM]?.[PermissionAction.UPDATE]) {
        queryArray.push(where('audience', '==', `${PermissionMapCode.SHOP_OTHERS_OCR_CONFIRM}_UPDATE`))
      }
      if (permissionConfig[PermissionMapCode.SHOP_DELIVERY_ORDER_ALL]?.[PermissionAction.CREATE]) {
        queryArray.push(where('audience', '==', `${PermissionMapCode.SHOP_DELIVERY_ORDER_ALL}_CREATE`))
      }
      if (permissionConfig[PermissionMapCode.SHOP_ISSUE_REPORT]?.[PermissionAction.VIEW]) {
        queryArray.push(where('audience', '==', `${PermissionMapCode.SHOP_ISSUE_REPORT}_VIEW`))
      }

      if (queryArray.length) {
        const queryInbox = query(inboxColRef, or(...queryArray), orderBy('updatedAt', 'desc'), limit(queryLimit))

        return queryInbox
      }
    },
    [currentUser?.userKey, currentBranchId, companyId, permissionConfig]
  )

  const subscribeInboxNotification = () => {
    const queryInbox = getQueryInbox(1)

    if (!queryInbox) return

    return onSnapshot(queryInbox, querySnapshot => {
      if (
        querySnapshot.size > 0 &&
        (!currentUser?.userKey ||
          (lastOpenedInboxInMilliseconds[currentUser.userKey] ?? 0) < querySnapshot.docs[0].data().updatedAt)
      ) {
        setHaveUnreadNotification(true)
        const data = querySnapshot.docs[0].data()
        setNotiData(data)
      } else {
        setMount(true)
      }
    })
  }

  const onClickInboxItem = (notification: INotification) => {
    const { type, deliveryOrderId, issueReportId } = notification

    const navigator: any = {
      pathname: `/${ShopMenuSlug.job}/detail`,
      query: { id: notification.jobId },
    }

    if (type === InboxType.ADMIN_FEEDBACK) {
      navigator.query['comment'] = new Date().getTime().toString()
      router.push(navigator)
    }

    if ([InboxType.ESTIMATE_PROCESSING, InboxType.ESTIMATE_COMPLETE].includes(type)) {
      router.push(navigator)
    }

    if ([InboxType.IDENTITY_VERIFIED, InboxType.IDENTITY_REJECTED].includes(type)) {
      navigator.query['view'] = 'process'
      router.push(navigator)
    }

    if (type === InboxType.IDENTITY_REQUESTED) {
      router.push({ pathname: `/${ShopMenuSlug.job}`, query: { status: 'pending-verification' } })
    }

    if ([InboxType.DELIVERY_ORDER_CONFIRMED, InboxType.DELIVERY_ORDER_SUCCCESS].includes(type)) {
      router.push({ pathname: `/${ShopMenuSlug.deliveryOrder}/detail`, query: { id: deliveryOrderId } })
    }

    if (type === InboxType.VOUCHER_REQUESTED) {
      router.push({ pathname: `/${ShopMenuSlug.job}`, query: { status: 'pending-voucher-verification' } })
    }

    if ([InboxType.ISSUE_REPORT_APPROVED, InboxType.ISSUE_REPORT_REJECTED].includes(type)) {
      router.push({
        pathname: `/${ShopMenuSlug.issueReport}/detail`,
        query: { id: issueReportId, fromNotification: true },
      })
    }

    openNotificationHandler(false)
  }

  const onCloseCommonModalAskNotiPermission = useCallback(() => {
    setNotiData(null)
    clearSystemModalOpenType()
    hideCommonModal()
  }, [])

  const getNotificationConfigFromNotiData = useCallback((notiData: INotification): ICallNotiProp => {
    const { jobId, shopUserName, deliveryOrderId, voucherId, type, issueReportId } = notiData
    const { title, message } = notificationConfigByType[type] ?? { title: '', message: '' }
    const id = issueReportId || voucherId || deliveryOrderId
    return {
      title: t(title),
      body: t(message, { jobId, name: shopUserName, id }),
      window,
    }
  }, [])

  return (
    <Drawer title={t('notification.notification')} isOpen={showInbox} onClose={() => openNotificationHandler(false)}>
      {!notifications.length ? (
        <div className="flex flex-col items-center justify-center h-full">
          <div className="images img-empty-notification w-[300px] h-[225px]"></div>
          <span className="text-t2-semi-bold">{'คุณยังไม่มีการแจ้งเตือนใหม่'}</span>
        </div>
      ) : (
        notifications.map(notification => (
          <div
            key={notification.updatedAt}
            className="flex p-4 border-b hover:cursor-pointer"
            onClick={() => onClickInboxItem(notification)}
          >
            {renderInboxIcon(notification, lastOpenInbox)}
            <div className="flex flex-col">
              {renderInboxMessage(t, notification)}
              <span className="text-b6-regular text-base-400">
                {isTimestampInCurrentDate(notification.updatedAt)
                  ? displayTime(new Date(notification.updatedAt))
                  : formatMillisecondsToDateTime(notification.updatedAt)}
              </span>
            </div>
          </div>
        ))
      )}
    </Drawer>
  )
}

export default NotificationInbox

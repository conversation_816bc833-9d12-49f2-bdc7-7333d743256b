import { useCallback, useEffect, useMemo, useState } from 'react'
import { ChoicesRestfull } from 'survey-core'
import { SectionImage } from './sectionImage'
import { initIdCardInfo, SectionDataIDCard } from './sectionIdCard'
import { SectionContact } from './sectionContact'
import { Button } from 'ui'
import { useUserStore } from '@/stores/userStore'
import { useJobStore } from '@/stores/jobStore'
import { useSystemStore } from '@/stores/systemStore'
import { getFirebaseToken, getInitialHeader } from '@/services/auth'
import { useRouter } from 'next/router'
import { useTranslation } from '@/hooks'
import { CustomerInfoType, JobStatus } from '@/services/models/jobs'
import { UpdateCustomerInfoJobBody } from 'contracts'
import { DateTime } from 'luxon'
import { ShopMenuSlug } from 'ui/src/models'
import { PermissionAction, PermissionMapCode } from 'utils'

export interface IOcrWith2AuthProps {}

export type IDataContact = { tel: string; email: string }
export type IDataIdCard = {
  prefix: string
  'first-name': string
  'last-name': string
  'id-card-number': string
  address: string
  province: string
  sub: string
  district: string
  subdistrict: string
  'card-expired-date': Date | undefined
  'card-issue-date': Date | undefined
  'lifetime-card': 'true'[] | undefined
  'birthday-date': Date | undefined
}

export type IMasterAddress = {
  districtcode: number
  provincecode: number
  subdistrictcode: number
  zipcode: string
}

export type IOCResult = {
  prefix_tha: string
  name_tha: string
  lastName_tha: string
  date_of_birth_tha: string
  date_of_issue_tha: string
  date_of_expiry_tha: string
  id_number_eng: string
  district1: string
  district2: string
  province: string
  address_tha: string
  prefix_eng: string
  name_eng: string
  lastname_eng: string
  date_of_birth_eng: string
  date_of_issue_eng: string
  date_of_expiry_eng: string
}

enum IStep {
  waitClickSubmit = 'wait-click-submit',
  waitCardInfo = 'has-image',
  waitContactInfo = 'has-image-and-card-info',
  readyToSend = 'ready',
}

const validateList = {
  'birthday-date': ['birthDate'],
  'card-expired-date': ['expireDate'],
  'card-issue-date': ['issueDate'],
  'id-card-number': ['identificationNumber'],
  'first-name': ['thaiName', 'firstName'],
  'last-name': ['thaiName', 'lastName'],
  // 'lifetime-card': [],
  prefix: ['thaiName', 'title'],
}

function checkIsEditBody(currentData: IDataIdCard | null, dataSubmit: any) {
  let isEdited = false

  if (currentData) {
    Object.keys(validateList).forEach(key => {
      const keyList = validateList[key as keyof typeof validateList]
      let bodyData = dataSubmit
      let ocrData = currentData[key as keyof IDataIdCard]
      keyList.forEach(bodyKey => {
        bodyData = bodyData[bodyKey]
      })

      if (['birthday-date', 'card-expired-date', 'card-issue-date'].includes(key) && ocrData) {
        ocrData = DateTime.fromJSDate(ocrData as Date).toFormat('dd/MM/yyyy')
      }

      if (ocrData !== bodyData) {
        isEdited = true
        if (key === 'card-expired-date') {
          const isLifetimeOcr = ocrData === undefined && currentData['lifetime-card']?.[0] === 'true'
          const isLifetimeBody = bodyData === '99/99/9999'
          if (isLifetimeOcr && isLifetimeBody) {
            isEdited = false
          }
        }
      }
    })
  } else {
    isEdited = true
  }

  return isEdited
}
export function OcrWith2Auth(props: IOcrWith2AuthProps) {
  const { t } = useTranslation('common')
  const router = useRouter()

  const { user: currentUser } = useUserStore()
  const { showCommonModal, hideCommonModal, permissionConfig } = useSystemStore()
  const {
    job,
    fetchJobById,
    rejectByCustomer,
    rejectByShop,
    identityRequest,
    setJob,
    verifyByManager,
    verifyByDipChip,
    clearJob,
  } = useJobStore()

  const { hasPermissionOcrConfirm } = useMemo(() => {
    const hasPermissionOcrConfirm =
      permissionConfig?.[PermissionMapCode.SHOP_OTHERS_OCR_CONFIRM]?.[PermissionAction.UPDATE] ?? false

    return { hasPermissionOcrConfirm }
  }, [permissionConfig])

  const [currentImage, setCurrentImage] = useState<string | null>(null)
  const [dataFromOcr, setDataFromOcr] = useState<any>(null)
  const [currentData, setCurrentData] = useState<IDataIdCard | null>(null)
  const [dataContact, setDataContact] = useState<IDataContact | null>(null)
  const [submit, setSubmit] = useState<{ [key: string]: string; step: IStep }>({ step: IStep.waitClickSubmit })
  const [isPhoneNumberFromJob, setIsPhoneNumberFromJob] = useState<boolean>(false)
  const currentId = useMemo(() => router.query.id as string, [router.query.id])

  const { canViewPage, canEditPage, needOCR, isManager, rejectReason } = useMemo(() => {
    const defaultResult = {
      canViewPage: false,
      canEditPage: false,
      needOCR: false,
      isManager: false,
      rejectReason: null,
    }
    const currentUserKey = currentUser?.userKey
    if (!(job && currentUserKey)) {
      return defaultResult
    }
    const { shopUserKey, status, contract } = job
    // -- status
    const isCanAccessStatus = [JobStatus.PRICE_ESTIMATED, JobStatus.IDENTITY_REJECTED].includes(status)
    if (!isCanAccessStatus) {
      return defaultResult
    }
    const isRejectStatus = status === JobStatus.IDENTITY_REJECTED
    // -- user
    const isOwner = currentUserKey === shopUserKey
    const isManager = hasPermissionOcrConfirm
    const canViewPage = isOwner || (isManager && isRejectStatus) // เจ้าของได้เสมอ manager ที่ไม่ใช่เจ้าของเปิดดูได้กรณี reject(แบบเป็น isReadOnly)
    const canEditPage = isOwner
    // -- mis config
    const needOCR = !isRejectStatus // กล้องจะส่งไป ocr หรือ fasle อัพเดทรูปอย่างเดียว -> ถ้ามี update ocr mount dataIdCard Section ใหม่
    return { canViewPage, canEditPage, needOCR, isManager, rejectReason: contract?.customerInfo?.rejectReason ?? '' }
  }, [job, currentUser])

  useEffect(() => {
    if (currentId) {
      fetchJobById(currentId)
    }

    return clearJob
  }, [currentId])

  useEffect(() => {
    if (job) {
      initSurveyAuth()
      if (job.contract) {
        const dataIdCardMount = { ...job.getSurveyIdCardInfo() }
        const dataContactMount = { ...job?.getSurveyContactInfo() }
        setCurrentImage(job.contract.customerInfo.photo)
        setCurrentData(dataIdCardMount)
        setDataContact(dataContactMount)
      }
    }
  }, [job])

  useEffect(() => {
    if (dataFromOcr) {
      const { ocrResult, masterAddress, phoneNumber } = dataFromOcr
      const dataInit = initIdCardInfo({ ocrResult, masterAddress })
      setCurrentData(dataInit)
      setDataContact({ tel: phoneNumber || '', email: '' })
      if (phoneNumber) {
        setIsPhoneNumberFromJob(true)
      }
    }
  }, [dataFromOcr])

  useEffect(() => {
    if (submit.step === IStep.readyToSend) {
      onSubmit(submit)
    }
  }, [submit.step])

  const initSurveyAuth = async (): Promise<void> => {
    const token = await getFirebaseToken()

    ChoicesRestfull.onBeforeSendRequest = (sender, options) => {
      const headers = getInitialHeader()
      options.request.setRequestHeader('Authorization', 'Bearer ' + token)
      options.request.setRequestHeader('x-branch', headers['x-branch'])
      options.request.setRequestHeader('x-company', headers['x-company'])
      options.request.setRequestHeader('Content-Type', 'application/json')
    }
  }

  // --- action handler

  const onSubmit = useCallback(
    async (dataSubmit: any) => {
      const { step, ...props } = dataSubmit
      const body: UpdateCustomerInfoJobBody = { ...props, isOcr: true }
      const rejectReason = job?.contract?.customerInfo?.rejectReason
      if (rejectReason) {
        body['rejectReason'] = rejectReason
      }

      const isEdited = checkIsEditBody(currentData, dataSubmit)

      if (isEdited || !needOCR) {
        // 2nd auth
        if (isManager) {
          const resIdentify = await identityRequest(currentId, body)
          const respVerify = await verifyByManager(currentId)
          if (resIdentify && respVerify) {
            router.replace({ pathname: '/jobs/campaign-select', query: { id: currentId } })
            setJob(undefined)
          }
        } else {
          const identityRequestJob = await identityRequest(currentId, body)
          if (identityRequestJob) {
            onSubmitSuccess()
          }
        }
      } else {
        // dipchip
        const res = await verifyByDipChip(currentId, { ...body, type: CustomerInfoType.DIP_CHIP }, 'tablet', true)
        if (res) {
          router.push({ pathname: `/${ShopMenuSlug.job}/campaign-select`, query: { id: currentId } })
        }
      }
    },
    [job, currentId, isManager, currentData, needOCR]
  )

  const onSubmitSuccess = useCallback(() => {
    router.replace({ pathname: '/jobs/detail', query: { id: currentId, view: 'process' } })
    setJob(undefined)
  }, [currentId])

  const onSubmitClick = useCallback(() => {
    setSubmit({ photo: currentImage ?? '', step: IStep.waitCardInfo })
  }, [currentImage])

  const onRejectClick = useCallback(() => {
    showCommonModal({
      title: t('customer-reject-full'),
      description: t('confirm-reject-full'),
      onClose: () => hideCommonModal(),
      onClickNegativeButton: () => hideCommonModal(),
      onClickPositiveButton: async () => {
        hideCommonModal()
        const resultJob = await rejectByCustomer(currentId)
        if (resultJob) {
          router.push({
            pathname: `/${ShopMenuSlug.tradesHistory}`,
            query: { status: 'rejected' },
          })
        }
      },
      buttonType: 'horizontal-duo',
      positiveButtonTxt: t('reject-trade'),
      negativeButtonTxt: t('back'),
      icon: 'ic-reject-red',
      iconCls: '!h-16 !w-16',
    })
  }, [currentId])

  const onCancelClick = useCallback(() => {
    showCommonModal({
      title: t('jobs-management.cancel-job'),
      description: t('confirm-reject'),
      onClose: () => hideCommonModal(),
      onClickNegativeButton: () => hideCommonModal(),
      onClickPositiveButton: async () => {
        hideCommonModal()
        const resultJob = await rejectByShop(currentId)
        if (resultJob) {
          router.push({
            pathname: `/${ShopMenuSlug.tradesHistory}`,
            query: { status: 'rejected' },
          })
        }
      },
      buttonType: 'horizontal-duo',
      positiveButtonTxt: t('ok'),
      negativeButtonTxt: t('back'),
      icon: 'ic-reject-red',
      iconCls: '!h-16 !w-16',
    })
  }, [currentId])

  return (
    <>
      {canViewPage && (
        <div>
          <SectionImage
            needOCR={needOCR}
            currentImage={currentImage}
            setCurrentImage={setCurrentImage}
            setOcrData={setDataFromOcr}
            isReadOnly={!canEditPage}
            rejectReason={rejectReason}
          />
          {currentData !== null && (
            <SectionDataIDCard
              currentData={currentData}
              isSubmit={submit.step === IStep.waitCardInfo}
              getBodyOcrData={(data: any) => {
                if (data === null) {
                  setSubmit({ step: IStep.waitClickSubmit })
                } else {
                  setSubmit((prev: any) => ({ ...prev, ...data, step: IStep.waitContactInfo }))
                }
              }}
              isReadOnly={!canEditPage}
            />
          )}
          {currentData !== null && (
            <SectionContact
              mountData={dataContact}
              isValidateOnly={submit.step === IStep.waitCardInfo}
              isSubmit={submit.step === IStep.waitContactInfo}
              getBodyOcrData={(data: any) => {
                if (data === null) {
                  setSubmit({ step: IStep.waitClickSubmit })
                } else if (submit.step === IStep.waitContactInfo) {
                  setSubmit((prev: any) => ({ ...prev, ...data, step: IStep.readyToSend }))
                }
              }}
              isReadOnly={!canEditPage}
              isPhoneNumberFromJob={isPhoneNumberFromJob}
            />
          )}

          {canEditPage && (
            <div className="flex justify-between mt-4">
              <Button colorScheme="primary" variant="outlined" cls="h-[48px] w-[168px]" onClick={onCancelClick}>
                {t('common.jobs-management.cancel-job')}
              </Button>
              <div className="flex gap-2">
                <Button colorScheme="primary" variant="outlined" cls="h-[48px] w-[168px]" onClick={onRejectClick}>
                  {t('common.verify.customer-reject')}
                </Button>
                <Button
                  colorScheme="primary"
                  variant="filled"
                  cls="h-[48px] w-[168px]"
                  onClick={onSubmitClick}
                  isDisabled={currentImage === null}
                >
                  {t('common.verify.verify-identity')}
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </>
  )
}

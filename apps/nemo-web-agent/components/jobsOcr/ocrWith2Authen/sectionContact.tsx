import { useCallback, useEffect, useMemo } from 'react'
import { Model, FunctionFactory, GetQuestionTitleActionsEvent } from 'survey-core'
// import hook
import { SurveyTemplate, Template } from '@/services/models/jobs'
import { OcrContactTemplate, theme } from '@/survey-forms'
import { PrefillEmptyField, scrollToFirstError } from '@/utils'
import { IDataContact } from '.'
import { GroupSection } from 'ui'
import { Survey } from 'survey-react-ui'
import { isPhoneNumberValid } from '@/utils/survey/validator'

FunctionFactory.Instance.register('isPhoneNumberValid', isPhoneNumberValid)

export interface ISectionContactProps {
  mountData: IDataContact | null
  getBodyOcrData: (data: any) => void
  isReadOnly: boolean
  isSubmit: boolean
  isValidateOnly: boolean
  isPhoneNumberFromJob: boolean
}

export function SectionContact({
  mountData,
  getBodyOcrData,
  isReadOnly,
  isSubmit,
  isValidateOnly,
  isPhoneNumberFromJob,
}: Readonly<ISectionContactProps>) {
  const addTooltip = (_: Model, options: GetQuestionTitleActionsEvent) => {
    if (options.question.tooltip) {
      options.titleActions = [
        {
          title: 'i',
          tooltip: JSON.stringify({
            title: options.question.tooltip.title,
            html: options.question.tooltip.html,
          }),
          action: () => {},
        },
      ]
    }
  }

  const survey = useMemo(() => {
    const template: Template = Template.toInstance(OcrContactTemplate)

    const qualifiedTemplate = PrefillEmptyField(template)
    const surveyModel = new Model(qualifiedTemplate.survey_form)
    const completeButton = surveyModel.navigationBar.actions.find(x => x.id === 'sv-nav-complete')
    surveyModel.applyTheme(theme)
    surveyModel.navigationBar.actions.splice(surveyModel.navigationBar.actions.indexOf(completeButton!), 1)
    surveyModel.hideRequiredErrors = true
    surveyModel.textUpdateMode = 'onTyping'
    // add tooltips id has tooltips node
    surveyModel.onGetQuestionTitleActions.add(addTooltip)
    surveyModel
      .getAllQuestions()
      .forEach(question => (question.cssClasses.hintSuffix = `${question.cssClasses.hintSuffix} !font-sans`))

    // book empty field if not 3 fields
    surveyModel.onAfterRenderQuestion.add(function (sender, options) {
      if (options.question.name === 'emptyField') {
        options.htmlElement.style.opacity = '0'
        options.htmlElement.style.height = '0px'
      }
    })
    if (mountData) {
      surveyModel.data = { ...mountData }
    }
    const allQuestions = surveyModel.getAllQuestions()
    allQuestions.forEach(question => {
      if (question.name === 'tel' && isPhoneNumberFromJob) {
        question.readOnly = true
      }
    })

    if (isReadOnly) {
      const allQuestions = surveyModel.getAllQuestions()
      allQuestions.forEach(question => {
        question.readOnly = true
      })
    }

    const data: SurveyTemplate = {
      slug: template.slug,
      title: template.title,
      subtitle: template.subtitle,
      model: surveyModel,
    }

    return data
  }, [isReadOnly, mountData])

  useEffect(() => {
    if (isValidateOnly && (survey === null || !onValidate(survey))) {
      getBodyOcrData(null)
    }
  }, [isValidateOnly])

  useEffect(() => {
    if (isSubmit) {
      onSubmit()
    }
  }, [isSubmit])

  const onSurveyValueChanged = (sender: Model, options: any) => {
    if (options.name === 'tel') {
      // Remove non-numeric characters
      const tel = options.value.replace(/\D/g, '').slice(0, 10)
      options.value = tel

      // Update the survey data with the cleaned value
      sender.setValue(options.name, options.value)
    }
  }

  const onValidate = useCallback((survey: SurveyTemplate): boolean => {
    let isValid: boolean = true

    const validate = survey.model.validate()
    if (!validate) {
      isValid = validate
    }

    scrollToFirstError()

    return isValid
  }, [])

  const onSubmit = async () => {
    if (survey === null || !onValidate(survey)) {
      getBodyOcrData(null)
      return
    }
    const contactInformation = survey.model.data

    const bodyOcr: any = {
      mobileNumber: contactInformation.tel || '',
      email: contactInformation.email || '',
    }
    getBodyOcrData(bodyOcr)
  }
  return (
    <div className="mt-4">
      {survey !== null && (
        <GroupSection key={survey.slug} isRounded={true} title={survey.title} subtitle={survey.subtitle} cls="mt-4">
          <div className="mt-4">
            <Survey model={survey.model} onValueChanged={onSurveyValueChanged} />
          </div>
        </GroupSection>
      )}
    </div>
  )
}

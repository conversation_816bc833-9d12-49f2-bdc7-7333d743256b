import { ModelMasterFunctionResult } from '@/services/models/modelMasterFunction'

import { DataTableField } from 'ui'
// import { formatPrice } from 'utils'
import { Tooltip } from '@material-tailwind/react'

const staticListFields = ({ t }: { t: (text: string) => string }): DataTableField[] => {
  const list = [
    {
      name: 'ownerName',
      label: t(`model-master-function.owner-name`),
      width: '130px',
      disableSort: false,
      formatter: (item: ModelMasterFunctionResult) => {
        return (
          <Tooltip content={item?.ownerName} placement="top-start">
            {item?.ownerName}
          </Tooltip>
        )
      },
    },
    {
      name: 'modelKey',
      label: t(`model-master-function.id`),
      width: '212px',
      disableSort: false,
      formatter: (item: ModelMasterFunctionResult) => {
        return (
          <Tooltip content={item?.id} placement="top-start">
            {item?.id}
          </Tooltip>
        )
      },
    },
    {
      name: 'brand',
      label: t(`model-master-function.brand`),
      width: '212px',
      cls: 'text-d6-semi-bold text-primary-500',
      disableSort: false,
      formatter: (item: ModelMasterFunctionResult) => {
        return item?.modelIdentifiers.brand
      },
    },
    {
      name: 'model',
      label: t(`model-master-function.model`),
      width: '212px',
      cls: 'text-d6-semi-bold text-primary-500',
      headerStyles: '!z-10',
      bodyStyles: '!z-10',
      disableSort: false,
      formatter: (item: ModelMasterFunctionResult) => {
        return (
          <Tooltip content={item?.modelIdentifiers.model} placement="top-start">
            {item?.modelIdentifiers.model}
          </Tooltip>
        )
      },
    },
    {
      name: 'rom',
      label: t(`model-master-function.rom`),
      width: '212px',
      cls: 'text-d6-semi-bold text-primary-500',
      disableSort: true,
      headerAlignment: 'text-left justify-center',
      textAlignment: 'text-center justify-center',
      formatter: (item: ModelMasterFunctionResult) => {
        return item?.modelIdentifiers.rom
      },
    },
  ]

  return list as DataTableField[]
}

export const modelMasterFunctionListFields = (
  t: (text: string) => string
  // penaltyColumn: string[]
): DataTableField[] => {
  return [...staticListFields({ t })]
}

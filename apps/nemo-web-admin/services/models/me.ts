import { autoserializeAs } from 'cerialize'
import {
  <PERSON>Role,
  GetMeResponse,
  PermissionDetail,
  PermissionGroupConfig,
  RoleConfig,
  RolePermission,
} from 'contracts/users'

export class MeResponse implements GetMeResponse {
  @autoserializeAs('userKey')
  userKey: string = ''
  @autoserializeAs('name')
  name: string = ''
  @autoserializeAs('branchRoles')
  branchRoles: BranchRoleModel[] = []
  @autoserializeAs('roleConfig')
  roleConfig: RoleConfigModel[] = []
  @autoserializeAs('permissionGroupConfig')
  permissionGroupConfig: PermissionGroupConfigModel[] = []
  @autoserializeAs('userType')
  userType: string = ''
  @autoserializeAs('vendorType')
  vendorType: string | null = null
}
export class BranchRoleModel implements BranchRole {
  @autoserializeAs('branchId')
  branchId: string = ''
  @autoserializeAs('branchName')
  branchName: string = ''
  @autoserializeAs('roles')
  roles: string[] = []
}

export class RoleConfigModel implements RoleConfig {
  @autoserializeAs('roleId')
  roleId: string = ''
  @autoserializeAs('rolePermissions')
  rolePermissions: RolePermissionModel[] = []
}

export class RolePermissionModel implements RolePermission {
  @autoserializeAs('permissionId')
  permissionId: string = ''
  @autoserializeAs('view')
  view: boolean = false
  @autoserializeAs('create')
  create: boolean = false
  @autoserializeAs('update')
  update: boolean = false
  @autoserializeAs('delete')
  delete: boolean = false
  @autoserializeAs('download')
  download: boolean = false
  @autoserializeAs('upload')
  upload: boolean = false
}

export class PermissionGroupConfigModel implements PermissionGroupConfig {
  @autoserializeAs('permissionGroupId')
  permissionGroupId: string = ''
  @autoserializeAs('permissionGroupLabel')
  label: string = ''
  @autoserializeAs('isInMenu')
  isInMenu: boolean = false
  @autoserializeAs('permissions')
  permissions: PermissionDetailModel[] = []
}

export class PermissionDetailModel implements PermissionDetail {
  @autoserializeAs('permissionId')
  permissionId: string = ''
  @autoserializeAs('label')
  label: string = ''
  @autoserializeAs('iconName')
  iconName: string = ''
  @autoserializeAs('iconNameActive')
  iconNameActive: string = ''
}

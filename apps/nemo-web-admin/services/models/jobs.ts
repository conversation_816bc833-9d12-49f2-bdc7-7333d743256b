import { autoserialize, autoserializeAs, Deserialize, inheritSerialization } from 'cerialize'
import { GetJobsResponse, IMonthlyInspection } from 'contracts'
import { BaseFilter } from 'utils'
import { ModelMasterGrade } from './modelMaster'
import { defaultValue } from '@/utils'
import { QCStatus } from '@/config/qc'
import { AllocationOrderResp } from './allocationOrder'
import { AOShippingStatus } from '@/config/allocationOrder'

export enum JobStatus {
  DRAFT = '00_DRAFT',
  QUOTE_REQUESTED = '10_QUOTE_REQUESTED',
  ESTIMATE_PRICE_PROCESSING = '11_ESTIMATE_PRICE_PROCESSING',
  PRICE_ESTIMATED = '12_PRICE_ESTIMATED',
  IDENTITY_REQUESTED = '20_IDENTITY_REQUESTED',
  IDENTITY_REJECTED = '21_IDENTITY_REJECTED',
  IDENTITY_VERIFIED = '30_IDENTITY_VERIFIED',
  CAMPAIGN_SELECTED = '35_CAMPAIGN_SELECTED',
  PURCHASED = '40_PURCHASED',
  RECEIVED = '50_RECEIVED',
  QC_COMPLETED = '60_QC_COMPLETED',
  REPAIR_ASSIGNED = '65_REPAIR_ASSIGNED',
  REPAIR_COMPLETED = '70_REPAIR_COMPLETED',
  INSPECTION_ASSIGNED = '75_INSPECTION_ASSIGNED',
  INSPECTION_FAILED = '79_INSPECTION_FAILED',
  INSPECTION_COMPLETED = '80_INSPECTION_COMPLETED',
  INSPECTION_AUTO_COMPLETED = '81_INSPECTION_AUTO_COMPLETED',
  AO_CREATED = '82_AO_CREATED',
  REJECT_BY_SHOP = '98_REJECT_BY_SHOP',
  REJECT_BY_CUSTOMER = '99_REJECT_BY_CUSTOMER',
}

export enum JobShippingStatus {
  SHIPPED = '00_SHIPPED',
  RECEIVED = '10_RECEIVED',
  RECEIVED_WITH_CONDITION = '11_RECEIVED_WITH_CONDITION',
  RECEIVED_OTHER = '12_RECEIVED_OTHER',
}

export enum JobConfirmPriceStatus {
  WAITING_CONFIRM_PRICE = 'WAITING_CONFIRM_PRICE',
  CONFIRMED_PRICE = 'CONFIRMED_PRICE',
}

export interface RepairHx {
  type: QCStatus.FIX | QCStatus.REFURBISH
  detail: string
  cost?: number
  grade?: 'A' | 'B' | 'C' | 'D'
  by: { key: string; name: string }
  at: string
  assignedAt?: string
}

export interface InspectHx {
  by: { key: string; name: string }
  at: Date
  detail?: string
  isPassed: boolean
  isQCScrap: boolean
}

export type AOShippingStatusFail = AOShippingStatus.NOT_SHIPPED | AOShippingStatus.NOT_SCANNED | AOShippingStatus.LOST

export interface AOListValue {
  allocationOrderId: string
  userKey: string
  userName: string
  remark: string
  type: AOShippingStatusFail
  confirmedAt: Date
  videoPath?: string
}

class ModelTemplate {
  @autoserializeAs(ModelMasterGrade, 'modelMasterGrades')
  modelMasterGrades!: ModelMasterGrade[]
}
class MobileModel {
  @autoserializeAs('brand')
  brand!: string
  @autoserializeAs('model')
  model!: string
  @autoserializeAs('rom')
  rom!: string
}

class BranchModel {
  @autoserializeAs('branchId')
  branchId!: string
  @autoserializeAs('title')
  title!: string
}

class AdminUserKeyModel {
  @autoserializeAs('userKey')
  userKey!: string
  @autoserializeAs('name')
  name!: string
}

export class FillSpaceColumn {
  @autoserializeAs('path')
  path!: string
  @autoserializeAs('count')
  count!: number
}

export class ShippingStatusMap {
  @autoserializeAs('label')
  label!: string
  @autoserializeAs('value')
  value!: string
}

export class Color {
  @autoserializeAs('id')
  id!: string
  @autoserializeAs('companyId')
  companyId!: string
  @autoserializeAs('nameTh')
  nameTh!: string
  @autoserializeAs('nameEn')
  nameEn!: string
  @autoserializeAs(Date, 'createdAt')
  updatedBy!: string
  @autoserializeAs(Date, 'updatedAt')
  updatedAt!: Date
}

class AdminUpdateCostListValueModel {
  @autoserializeAs('at')
  at!: Date
  @autoserializeAs('by')
  by!: any
  @autoserializeAs('retailPrice')
  retailPrice!: number
  @autoserializeAs('wholeSalePrice')
  wholeSalePrice!: number
}

export class MonthlyInspection implements IMonthlyInspection {
  @autoserialize
  date!: Date
  @autoserialize
  allProduct!: number
  @autoserialize
  aGradeProduct!: number
  @autoserialize
  otherGradeProduct!: number
  @autoserialize
  scrapProduct!: number
  @autoserialize
  completeStatus!: boolean
}
export class Template {
  @autoserializeAs('slug')
  slug!: string
  @autoserializeAs('title')
  title!: string
  @autoserializeAs('title_detail')
  title_detail!: string
  @autoserializeAs('survey_form')
  survey_form!: any
  @autoserializeAs('fillSpaceColumnCount')
  fillSpaceColumnCount!: number
  @autoserializeAs(FillSpaceColumn, 'fillSpaceColumn')
  fillSpaceColumn!: FillSpaceColumn[]
}

export class PDF {
  @autoserializeAs('base64')
  base64!: string
}

export class Job implements GetJobsResponse {
  @autoserializeAs('companyId')
  companyId!: string
  @autoserializeAs('jobId')
  jobId!: string
  @autoserializeAs('deviceKey')
  deviceKey!: string
  @autoserializeAs('deviceKey2')
  deviceKey2!: string
  @autoserializeAs('branchId')
  branchId!: string
  @autoserializeAs('modelKey')
  modelKey!: string
  @autoserializeAs(MobileModel, 'modelKey')
  mobileModel!: MobileModel
  @autoserializeAs('thaiId')
  thaiId!: string
  @autoserializeAs('createdBy')
  createdBy!: string
  @autoserializeAs('updatedBy')
  updatedBy!: string
  @autoserializeAs('shopUserName')
  shopUserName!: string
  @autoserializeAs('adminUserName')
  adminUserName!: string
  @autoserializeAs('adminUserKey')
  adminUserKey!: string
  @autoserializeAs('status')
  status!: string
  @autoserializeAs('vendorType')
  vendorType!: string
  @autoserializeAs(MobileModel, 'modelIdentifiers')
  modelIdentifiers!: MobileModel
  @autoserializeAs(ModelTemplate, 'modelTemplate')
  modelTemplate!: ModelTemplate
  @autoserializeAs(Template, 'checkList')
  checkList!: Template[]
  @autoserializeAs('checkListValues')
  checkListValues!: any
  @autoserializeAs('adminCheckListValues')
  adminCheckListValues!: any
  @autoserializeAs('penalties')
  penalties!: any
  @autoserializeAs('suggestedPrice')
  suggestedPrice!: number
  @autoserializeAs('purchasedPrice')
  purchasedPrice!: number
  @autoserializeAs('isAdditionalCheckList')
  isAdditionalCheckList!: boolean
  @autoserializeAs('branch')
  branch!: BranchModel
  @autoserializeAs('createdAt')
  createdAt!: Date
  @autoserializeAs('updatedAt')
  updatedAt!: Date
  @autoserializeAs('adminUser')
  adminUser!: AdminUserKeyModel
  @autoserializeAs('requestedAt')
  requestedAt!: Date
  @autoserializeAs('assignedAt')
  assignedAt!: Date
  @autoserializeAs('estimatedAt')
  estimatedAt!: Date
  @autoserializeAs('purchasedAt')
  purchasedAt!: Date
  @autoserializeAs('rejectedAt')
  rejectedAt!: Date
  @autoserializeAs('completedShopAt')
  completedShopAt!: Date
  @autoserializeAs('deliveryOrderId')
  deliveryOrderId?: string
  @autoserializeAs('deliveryOrder')
  deliveryOrder?: any
  @autoserializeAs('shippingStatus')
  shippingStatus?: string
  @autoserializeAs('receiverUserKey')
  receiverUserKey?: string
  @autoserializeAs('receivingRemark')
  receivingRemark?: string
  @autoserializeAs('receivedAt')
  receivedAt?: Date
  @autoserializeAs('qcStatus')
  qcStatus?: string
  @autoserializeAs('qcBy')
  qcBy?: string
  @autoserializeAs('qcAt')
  qcAt?: Date
  @autoserializeAs('qcUser')
  qcUser?: AdminUserKeyModel
  @autoserializeAs('repairedBy')
  repairedBy?: string
  @autoserializeAs('repairedAt')
  repairedAt?: Date
  @autoserializeAs('assignRepairAt')
  assignRepairAt?: Date
  @autoserializeAs('repairListValue')
  repairListValue?: RepairHx[]
  @autoserializeAs('repairedUser')
  repairedUser?: AdminUserKeyModel
  @autoserializeAs('currentGrade')
  currentGrade?: string
  @autoserializeAs('inspectListValue')
  inspectListValue?: InspectHx[]
  @autoserializeAs('inspectedBy')
  inspectedBy?: string
  @autoserializeAs('assignInspectAt')
  assignInspectAt?: Date
  @autoserializeAs('inspectedAt')
  inspectedAt?: Date
  @autoserializeAs('inspectedUser')
  inspectedUser?: AdminUserKeyModel
  @autoserializeAs('updatedUser')
  updatedUser?: AdminUserKeyModel
  @autoserializeAs('costPrice')
  costPrice?: number
  @autoserializeAs('retailPrice')
  retailPrice?: number
  @autoserializeAs('wholeSalePrice')
  wholeSalePrice?: number
  @autoserializeAs('retailMargin')
  retailMargin?: number
  @autoserializeAs('wholeSaleMargin')
  wholeSaleMargin?: number
  @autoserializeAs('marginRetailBaht')
  marginRetailBaht?: number
  @autoserializeAs('marginWholeSaleBaht')
  marginWholeSaleBaht?: number
  @autoserializeAs('modelMaster')
  modelMaster?: any
  @autoserializeAs('estimatedGrade')
  estimatedGrade?: string
  @autoserializeAs('adminUpdateCostListValue')
  adminUpdateCostListValue?: AdminUpdateCostListValueModel[]
  @autoserializeAs('allocationOrderId')
  allocationOrderId?: string
  @autoserializeAs('aoShippingStatus')
  aoShippingStatus?: string
  @autoserializeAs('aoReceivedAt')
  aoReceivedAt?: Date
  @autoserializeAs('isConfirmPrice')
  isConfirmPrice!: boolean
  @autoserializeAs('allocationOrder')
  allocationOrder?: AllocationOrderResp
  @autoserializeAs('incompleteAOListValue')
  incompleteAOListValue?: AOListValue[]

  getMaxPrice(): number {
    const price: string = defaultValue(
      this.modelTemplate.modelMasterGrades.find(modelMaster => modelMaster.grade === 'A')?.purchasePrice,
      '0'
    )
    return Number(price)
  }

  static toInstance(json: any): Job {
    return Deserialize(json, this)
  }

  public static toParams = (data: any) => {
    return {
      ...data,
    }
  }
}

@inheritSerialization(BaseFilter)
export class JobFilter extends BaseFilter {
  @autoserialize
  public jobId?: string
  @autoserialize
  public deviceKey?: string
  @autoserialize
  public status?: string

  constructor() {
    super()
  }

  public get filterCount(): number {
    let r = 0
    if (this.jobId) {
      r++
    }
    return r
  }

  public get hash(): string {
    return `${this.jobId}`
  }
}

import { ListPagination, BaseService, serviceScheme } from 'utils'
import { getFirebaseToken, getInitialHeader, refreshFirebaseToken } from './auth'
import { ModelMastersTableResponse } from './models/modelMaster'
import { GetModelMastersTableResponse, ModelMasterGradeDetailResponse } from 'contracts'
import { ParsedUrlQuery } from 'querystring'

// Custom interface for the detailed model master response
export interface ModelMasterDetailResponse
  extends Omit<GetModelMastersTableResponse, 'gradeA' | 'gradeB' | 'gradeC' | 'gradeD'> {
  modelIdentifiers?: {
    brand: string
    model: string
    rom: string
  }
  systemCode?: string
  systemCodeList?: string[]
  modelMasterGrades?: ModelMasterGradeDetailResponse[]
}

export class ModelMasterRepository extends BaseService {
  public constructor(baseUrl: string = process.env.NEXT_PUBLIC_NEMO_SERVICE_HOST ?? '') {
    super(
      `${baseUrl}`,
      // Override path list for mapping data
      serviceScheme,
      false,
      {
        getToken: () => getFirebaseToken(),
        refreshToken: () => refreshFirebaseToken(),
      },
      getInitialHeader
    )
  }
  public async fetchModelMasters(queryContext?: ParsedUrlQuery): Promise<ListPagination<ModelMastersTableResponse>> {
    let urlSearchParams = new URLSearchParams()
    if (queryContext) {
      urlSearchParams = new URLSearchParams(queryContext as Record<string, string>)
    }
    const queryParams = urlSearchParams
    const resp = await this.get('api/core/v1/admin/model-masters', { queryParams })
    return resp.mapListPageDeserialize(ModelMastersTableResponse)
  }

  public async fetchModelMasterDetailById(id: string): Promise<ModelMasterDetailResponse | { errors: any }> {
    const resp = await this.get(`api/core/v1/admin/model-masters/${id}`)
      .then(resp => {
        return resp.resp.data
      })
      .catch(error => {
        return { errors: error.body }
      })
    return resp
  }

  public async fetchModelMasterDetailFuctionById(id: string): Promise<any> {
    const resp = await this.get(`api/core/v1/admin/model-master-function/${id}/function`)
      .then(resp => {
        return resp.resp.data
      })
      .catch(error => {
        return { errors: error.body }
      })
    return resp
  }

  public async fetchModelMasterDetailQuestionById(id: string): Promise<any> {
    const resp = await this.get(`api/core/v1/admin/model-master-function/${id}/question`)
      .then(resp => {
        return resp.resp.data
      })
      .catch(error => {
        return { errors: error.body }
      })
    return resp
  }

  public async updateModelMaster(id: string, body: any): Promise<any> {
    const resp = await this.put(`api/core/v1/admin/model-masters/${id}`, JSON.stringify(body))
      .then(resp => {
        return resp.resp.data
      })
      .catch(error => {
        return { errors: error.body }
      })
    return resp
  }

  // export example -> should move to modelPriceService
  // async export() {
  //   const url = this.cleanUrl('api/core/v1/admin/model-masters/export', undefined)
  //   return this._fetch(url as any, {
  //     method: 'get',
  //     headers: await this.getHeaders({
  //       'Content-Type': 'application/octet-stream', //'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //     }),
  //   })
  // }

  async export() {
    const resp = await this.get('api/core/v1/admin/model-masters/export')
    return resp.resp.data
  }

  async exportAvgCost() {
    const resp = await this.get('api/core/v1/admin/model-masters/export/average-cost')
    return resp.resp.data
  }
}

import { useTranslation } from '@/hooks'
import { ReactNode, useCallback, useEffect, useState } from 'react'
import { Button, Loading, UnderlineTabs as TabSelection } from 'ui'
import { LayoutPage } from '../layout'
import { DetailPriceProductTab } from './detailPriceProductTab'
import { DetailFunctionTab } from './datailFunctionTab'
import { DetailQuestionTab } from './datailQuestionsTab'
import { useRouter } from 'next/router'
import { AdminMenuSlug } from 'ui/src/models'
import { useSystemStore } from '@/stores/systemStore'
import { ILeavePageStatus, useLeavePage } from '@/hooks/useLeavePage'
import { NemoService } from '@/services/repositories'
import { useAfterSaleStore } from '@/stores/afterSaleStore'
import { TagInput } from 'ui'
import { scrollToFirstError, validateModelPriceGrades } from '@/utils'
import { PermissionMapCode } from 'utils'

const forbiddenCharList = [';']

export enum TagBoxErrorType {
  INPUT_FIELD_NOT_EMPTY = 'INPUT_FIELD_NOT_EMPTY',
  EMPTY_TAG = 'EMPTY_TAG',
}
export enum QuestionType {
  SELECTION = 'SELECTION',
}

interface PenaltyItem {
  checkListId?: string
  functionalPenalties?: string
  nonFunctionalPenalties?: string
  skipPenalties?: string
  functionalKeyCond?: string
  nonFunctionalKeyCond?: string
  skipKeyCond?: string
}

interface UpdatedItem {
  keyCond: string | undefined
  penalties: string | undefined
  action: string
}

interface IModelPriceFunctionDetailProps {
  title: string
}

export enum IModalType {
  LEAVE_PAGE = 'LEAVE_PAGE',
}

export enum ModelPriceFunctionTab {
  PRODUCTPRICE = 'product-price',
  FUNCTIONALITY = 'functionality',
  QUESTIONSABOUTTHEMACHINE = 'questions_about_the_machine',
}

interface LabelInputProps {
  children?: ReactNode
  label?: string
  showEditBtn?: boolean
  keyName?: string
}

export interface IDataTabFunctionWork {
  checkListId: string
  checked: boolean
  checklistNameEn: string
  checklistNameTh: string
  checklistType: string
  companyId: string
  functionKey: string
  functionSection: string
  functionalKeyCond: string
  functionalPenalties: string
  modelKey: string
  nonFunctionalKeyCond: string
  nonFunctionalPenalties: string
  skipKeyCond: string
  skipPenalties: string
}

export interface QuestionChoice {
  id: string
  answerEn: string
  answerTh: string
  penalties: string
  keyCond: string
}

export interface IDataTabQuestion {
  checkListId: string
  companyId: string
  modelKey: string
  functionKey: string
  functionSection: string
  checklistType: string
  checklistNameTh: string
  checklistNameEn: string
  questionType: string
  questionChoices: QuestionChoice[]
  checked: boolean
  skipPenalties: string
  skipKeyCond: string
}

export interface IModelMasterGrades {
  grade: string
  purchasePrice: string
  lastPurchasedOn: string
  lastPurchasedPrice: string
}
export interface IDataSubmitAll {
  modelMasterGrades: IModelMasterGrades[]
  dataTabFunctionWork: IDataTabFunctionWork[]
  dataTabQuestion: IDataTabQuestion[]
  modelIdentifiers: {
    brand: string
    model: string
    rom: string
  }
  modelKey: string
  modelYear: string
  systemCode: string
  systemCodeList: string[]
  matCode: string
  purchasedRatio: string
  referencePrice: string
  companyId: string
  ownerName: string
}

export interface ISaveMasterModel {
  companyId: string | undefined
  modelKey: string | undefined
  modelMaster: {
    systemCode: string | undefined
    systemCodeList: string[] | undefined
    modelMasterGrades: IModelMasterGrades[] | undefined
  }
  modelMasterFunction: UpdatedItem[]
}

const InputLayout: React.FC<LabelInputProps> = ({ children, label, showEditBtn, keyName }) => {
  if (keyName === 'blank') {
    return <div key={label}></div>
  } else {
    return (
      <div className="flex flex-col gap-1">
        <p className="text-base text-[#667085]">{label}</p>
        <p className={showEditBtn ? 'text-t4-semi-bold text-base-600' : ''}>{children}</p>
      </div>
    )
  }
}

const InputCustom = ({
  item,
  onChange,
  isError,
  disabled,
  placeholder,
  onClick,
}: {
  item: string | string[] | undefined
  onChange: (value: any) => void
  isError?: any
  disabled: boolean
  placeholder: string
  onClick: (value: any) => void
}) => {
  return (
    <input
      onClick={onClick}
      className={`w-[100%] rounded-lg border py-2.5 px-4 focus:outline-none placeholder:text-base-400 text-base-800 focus:text-base-700 h-11 read-only:disabled:text-base-400 read-only:disabled:bg-base-100 
        ${isError && 'border border-red-500'}
        `}
      onChange={e => onChange(e)}
      type="text"
      value={item}
      id="input-price-product-tab"
      disabled={disabled}
      placeholder={placeholder}
    />
  )
}

export function ModelPriceFunctionDetail({ title }: IModelPriceFunctionDetailProps) {
  const { t } = useTranslation('common')
  const { showToast, showCommonModal, hideCommonModal } = useSystemStore()
  const { clearToastOpenType } = useAfterSaleStore()
  const { leavePageStatus, setNeedAlertCondition, onConfirmRouteChange, onRejectRouteChange } = useLeavePage({})
  const router = useRouter()
  const id = router.query.id || ''
  const [isChangeForm, setIsChangeForm] = useState(false)
  const [tabValue, setTabValue] = useState<string>(ModelPriceFunctionTab.PRODUCTPRICE)
  const [focusedIndex, setFocusedIndex] = useState<{ index: number; key: string } | null>(null)
  const [showEditBtn, setShowEditBtn] = useState(true)
  const [modalType, setModalType] = useState<IModalType | null>()
  const [isLoadingAll, setIsLoadingAll] = useState<boolean>(true)
  const [isSubmit, setIsSubmit] = useState<boolean>(false)
  const [dataAllFormApi, setDataAllFormApi] = useState<IDataSubmitAll | undefined>(undefined)
  const [dataSubmitAll, setDataSubmit] = useState<IDataSubmitAll | undefined>(undefined)
  const [isLoadingTabFunctionWork, setIsLoadingTabFunctionWork] = useState<boolean>(false)
  const [isTextBoxTagEmpty, setIsTextBoxTagEmpty] = useState<boolean>(true)
  const [errorTextBoxTag, setErrorTextBoxTag] = useState<TagBoxErrorType | null>(null)

  const [dataSelectedValuesTabWorkFuction, setDataSelectedValuesTabWorkFuction] = useState<
    IDataTabFunctionWork[] | undefined
  >(undefined)

  const [dataSelectedValuesTabQuestion, setDataSelectedValuesTabQuestion] = useState<IDataTabQuestion[] | undefined>(
    undefined
  )

  useEffect(() => {
    getAllApi()
  }, [])

  useEffect(() => {
    if (leavePageStatus === ILeavePageStatus.confirmNeed) {
      setModalType(IModalType.LEAVE_PAGE)
    }
  }, [leavePageStatus])

  useEffect(() => {
    if (isChangeForm) {
      setNeedAlertCondition(true)
    } else {
      setNeedAlertCondition(false)
    }
  }, [isChangeForm])

  useEffect(() => {
    if (modalType === IModalType.LEAVE_PAGE) {
      showCommonModal({
        type: 'warning',
        buttonType: 'horizontal-duo',
        title: t('model-master-table.leave-page-title'),
        description: t('model-master-table.leave-page-description'),
        onClose: () => {
          onCloseCommonModal()
          onRejectRouteChange()
        },
        onClickNegativeButton: () => {
          onCloseCommonModal()
          onRejectRouteChange()
        },
        onClickPositiveButton: () => {
          onCloseCommonModal()
          onConfirmRouteChange()
        },
        negativeButtonTxt: t('cancel'),
        positiveButtonTxt: t('common.confirm-abandon'),
      })
    }
  }, [modalType])

  const handleIsTextBoxTagEmpty = (data: boolean) => {
    setIsTextBoxTagEmpty(data)
    setErrorTextBoxTag(null)
  }

  const onChangeTagInputField = useCallback(
    (fieldName: string, value: string[]) => {
      setDataSubmit((prevState: any) => {
        return {
          ...prevState,
          [fieldName]: value,
        }
      })

      if (dataAllFormApi && fieldName === 'systemCodeList') {
        if (dataAllFormApi.systemCodeList?.length !== value.length) {
          setIsChangeForm(true)
        }

        for (let i = 0; i < dataAllFormApi.systemCodeList?.length; i++) {
          if (dataAllFormApi.systemCodeList[i] !== value[i]) {
            setIsChangeForm(true)
          }
        }
      }
    },
    [dataAllFormApi]
  )

  const itemHeader = [
    {
      name: 'id',
      label: t('model-master-table.id'),
      onChange: '',
      value: dataSubmitAll?.modelKey,
      type: 'input',
      option: [],
      disable: true,
      placeholder: '',
    },
    {
      name: 'mat-code',
      label: t('model-master-table.mat-code'),
      onChange: '',
      type: 'input',
      value: dataSubmitAll?.matCode,
      option: [],
      disable: true,
      placeholder: '',
    },
    {
      name: 'brand',
      label: t('model-master-table.brand'),
      onChange: '',
      type: 'input',
      value: dataSubmitAll?.modelIdentifiers?.brand,
      option: [],
      disable: true,
      placeholder: t('model-master-table.please-select-brand'),
    },
    {
      name: 'model',
      label: t('model-master-table.model'),
      onChange: '',
      type: 'input',
      value: dataSubmitAll?.modelIdentifiers?.model,
      option: [],
      disable: true,
      placeholder: t('model-master-table.please-select-model'),
    },
    {
      name: 'rom',
      label: t('model-master-table.rom'),
      onChange: '',
      type: 'input',
      value: dataSubmitAll?.modelIdentifiers?.rom,
      option: [],
      disable: true,
      placeholder: t('model-master-table.please-select-rom'),
    },
    {
      name: 'owner-name',
      label: t('model-master-table.owner-name'),
      onChange: '',
      type: 'input',
      value: dataSubmitAll?.ownerName,
      option: [],
      disable: true,
      placeholder: '',
    },
    {
      name: 'system-code',
      label: t('model-master-table.system-code'),
      onChange: 'systemCode',
      type: 'input',
      value: dataSubmitAll?.systemCodeList,
      option: [],
      disable: false,
      placeholder: t('model-master-table.error-text-system-code'),
    },
  ]

  const getAllApi = () => {
    getApiPriceProduct()
    getModelMasterDetailFuctionById()
    getModelMasterDetailQuestionById()
  }

  const getApiPriceProduct = async () => {
    setIsLoadingAll(true)
    const result = await NemoService.repositories.modelMaster.fetchModelMasterDetailById(String(id))

    if ('errors' in result) {
      // Handle error case
      setIsLoadingAll(false)
      return
    }

    // result is now properly typed as ModelMasterDetailResponse
    const gradeOrder = ['A', 'B', 'C', 'D']
    const gradeOrderData = result?.modelMasterGrades?.sort(
      (a: { grade: string }, b: { grade: string }) => gradeOrder.indexOf(a.grade) - gradeOrder.indexOf(b.grade)
    )

    setDataAllFormApi((prevState: any) => {
      return {
        ...prevState,
        modelMasterGrades: gradeOrderData,
        modelIdentifiers: result?.modelIdentifiers,
        modelKey: result?.modelKey,
        modelYear: result?.modelYear,
        systemCode: result?.systemCode,
        systemCodeList: result?.systemCodeList,
        referencePrice: result?.referencePrice,
        matCode: result?.matCode,
        purchasedRatio: result?.purchasedRatio,
        companyId: result?.companyId,
        ownerName: result?.ownerName,
      }
    })
    setDataSubmit((prevState: any) => {
      return {
        ...prevState,
        modelMasterGrades: gradeOrderData,
        modelIdentifiers: result?.modelIdentifiers,
        modelKey: result?.modelKey,
        modelYear: result?.modelYear,
        systemCode: result?.systemCode,
        systemCodeList: result?.systemCodeList,
        referencePrice: result?.referencePrice,
        matCode: result?.matCode,
        purchasedRatio: result?.purchasedRatio,
        companyId: result?.companyId,
        ownerName: result?.ownerName,
      }
    })
    setIsLoadingAll(false)
  }

  const getModelMasterDetailFuctionById = async () => {
    setIsLoadingTabFunctionWork(true)
    const result = await NemoService.repositories.modelMaster.fetchModelMasterDetailFuctionById(String(id))
    if (!result?.errors) {
      const IsChecked = result?.filter((itemFilter: IDataTabFunctionWork) => itemFilter.checked)

      setDataAllFormApi((prevState: any) => {
        return {
          ...prevState,
          dataTabFunctionWork: result,
        }
      })
      setDataSubmit((prevState: any) => {
        return {
          ...prevState,
          dataTabFunctionWork: result,
        }
      })
      setDataSelectedValuesTabWorkFuction(IsChecked)
      setIsLoadingTabFunctionWork(false)
    } else {
      setIsLoadingTabFunctionWork(false)
    }
  }

  const getModelMasterDetailQuestionById = async () => {
    setIsLoadingTabFunctionWork(true)
    const result = await NemoService.repositories.modelMaster.fetchModelMasterDetailQuestionById(String(id))

    if (!result?.errors) {
      const IsChecked = result?.filter((itemFilter: IDataTabQuestion) => itemFilter.checked)
      const mapResultForQuestionTypeSelect = result?.filter(
        (itemFiter: IDataTabQuestion) => itemFiter?.questionType === QuestionType.SELECTION
      )

      setDataAllFormApi((prevState: any) => {
        return {
          ...prevState,
          dataTabQuestion: mapResultForQuestionTypeSelect,
        }
      })
      setDataSubmit((prevState: any) => {
        return {
          ...prevState,
          dataTabQuestion: mapResultForQuestionTypeSelect,
        }
      })
      setDataSelectedValuesTabQuestion(IsChecked)
      setIsLoadingTabFunctionWork(false)
    } else {
      setIsLoadingTabFunctionWork(false)
      setDataAllFormApi((prevState: any) => {
        return {
          ...prevState,
          dataTabQuestion: [],
        }
      })
      setDataSubmit((prevState: any) => {
        return {
          ...prevState,
          dataTabQuestion: [],
        }
      })
      setDataSelectedValuesTabQuestion([])
    }
  }

  const saveModelMaster = async (data: ISaveMasterModel) => {
    setIsLoadingAll(true)
    const result = await NemoService.repositories.modelMaster.updateModelMaster(String(id), data)

    if (!result?.errors) {
      showToast({
        type: 'success',
        title: t('model-master-table.save-success'),
        message: '',
      })
      clearToastOpenType()
      setShowEditBtn(true)
      setIsChangeForm(false)
      getAllApi()
    } else {
      showToast({
        type: 'error',
        title: t('model-master-table.save-not-success'),
        message: '',
      })
      clearToastOpenType()
    }
    setIsLoadingAll(false)
  }

  const renderDetailProductForm = () => {
    return itemHeader.map((itemMap, index) => {
      if (itemMap.type === 'input') {
        if (itemMap.name === 'system-code') {
          return (
            <div key="system-code" className="tag-box">
              <TagInput
                field={{
                  name: 'name',
                  label: t('master-price.system-code'),
                  type: 'text',
                  required: false,
                  readonly: showEditBtn,
                  max: 100,
                  forbiddenCharList: forbiddenCharList,
                  key: 'system-code',
                  placeholder: t('model-master-table.error-text-system-code'),
                  helpText: t('master-price.tag-hint-text'),
                }}
                value={itemMap.value}
                onChange={value => onChangeTagInputField('systemCodeList', value)}
                error={
                  errorTextBoxTag === TagBoxErrorType.EMPTY_TAG
                    ? t('model-master-table.error-text-system-code')
                    : errorTextBoxTag === TagBoxErrorType.INPUT_FIELD_NOT_EMPTY
                      ? t('master-price.tag-hint-error-text')
                      : ''
                }
                setIsTextBoxTagEmpty={handleIsTextBoxTagEmpty}
              ></TagInput>
            </div>
          )
        } else if (showEditBtn) {
          return (
            <InputLayout key={itemMap.label} label={itemMap.label} showEditBtn={showEditBtn} keyName={itemMap.name}>
              {itemMap.value || '-'}
            </InputLayout>
          )
        } else {
          return (
            <InputLayout label={itemMap.label} key={itemMap.label} keyName={itemMap.name}>
              <InputCustom
                item={itemMap.value}
                placeholder={itemMap.placeholder}
                onChange={e => {
                  setDataSubmit((prevState: any) => {
                    return {
                      ...prevState,
                      [itemMap.onChange]: e.target.value,
                    }
                  })
                  setIsChangeForm(true)
                  setIsSubmit(false)
                }}
                onClick={() => setFocusedIndex(null)}
                disabled={itemMap.disable}
              />
            </InputLayout>
          )
        }
      }
    })
  }

  function compareDataForModelMasterFunction(
    selectedValues: PenaltyItem[] | undefined,
    allDataFromApi: PenaltyItem[] | undefined
  ): UpdatedItem[] {
    const updatedItems: UpdatedItem[] = []

    const processPenalties = (
      keyCond: string | undefined,
      penalties: string | undefined,
      action: 'CREATE' | 'UPDATE' | 'DELETE'
    ) => {
      if (penalties) {
        updatedItems.push({ keyCond, penalties, action })
      }
    }

    // ตรวจสอบการสร้างรายการ (CREATE)
    selectedValues?.forEach(selectedItem => {
      const apiItem = allDataFromApi?.find(item => item.checkListId === selectedItem.checkListId)

      if (!apiItem) {
        processPenalties(selectedItem.functionalKeyCond, selectedItem.functionalPenalties, 'CREATE')
        processPenalties(selectedItem.nonFunctionalKeyCond, selectedItem.nonFunctionalPenalties, 'CREATE')
        processPenalties(selectedItem.skipKeyCond, selectedItem.skipPenalties, 'CREATE')
      }
    })

    // ตรวจสอบการอัปเดตรายการ (UPDATE)
    selectedValues?.forEach(selectedItem => {
      const apiItem = allDataFromApi?.find(item => item.checkListId === selectedItem.checkListId)

      if (apiItem) {
        if (JSON.stringify(selectedItem.functionalPenalties) !== JSON.stringify(apiItem.functionalPenalties)) {
          processPenalties(selectedItem.functionalKeyCond, selectedItem.functionalPenalties, 'UPDATE')
        }
        if (JSON.stringify(selectedItem.nonFunctionalPenalties) !== JSON.stringify(apiItem.nonFunctionalPenalties)) {
          processPenalties(selectedItem.nonFunctionalKeyCond, selectedItem.nonFunctionalPenalties, 'UPDATE')
        }
        if (JSON.stringify(selectedItem.skipPenalties) !== JSON.stringify(apiItem.skipPenalties)) {
          processPenalties(selectedItem.skipKeyCond, selectedItem.skipPenalties, 'UPDATE')
        }
      }
    })

    // ตรวจสอบการลบรายการ (DELETE)
    allDataFromApi?.forEach(apiItem => {
      const selectedItem = selectedValues?.find(item => item.checkListId === apiItem.checkListId)

      if (!selectedItem) {
        processPenalties(apiItem.functionalKeyCond, apiItem.functionalPenalties, 'DELETE')
        processPenalties(apiItem.nonFunctionalKeyCond, apiItem.nonFunctionalPenalties, 'DELETE')
        processPenalties(apiItem.skipKeyCond, apiItem.skipPenalties, 'DELETE')
      }
    })

    return updatedItems
  }

  function compareDataForModelMasterQuestion(
    dataAllFormApi: IDataTabQuestion[] | undefined,
    dataSelectedValuesTabQuestion: IDataTabQuestion[] | undefined
  ) {
    const updates: { keyCond: string; penalties: string; action: string }[] = []

    // Helper function for adding updates
    const addUpdate = (keyCond: string, penalties: string, action: string) => {
      updates.push({ keyCond, penalties, action })
    }

    // Process questionChoices based on provided action
    const processQuestionChoices = (
      questionChoices: any[],
      action: 'CREATE' | 'DELETE',
      skipKeyCond: string,
      skipPenalties: string
    ) => {
      questionChoices.forEach(choice => addUpdate(choice.keyCond, choice.penalties, action))
      addUpdate(skipKeyCond, skipPenalties, action)
    }

    // Compare for creates (action: "CREATE")
    dataSelectedValuesTabQuestion?.forEach(selectedItem => {
      const matchedItem = dataAllFormApi?.find(item => item.checkListId === selectedItem.checkListId)
      if (!matchedItem) {
        processQuestionChoices(
          selectedItem.questionChoices,
          'CREATE',
          selectedItem.skipKeyCond,
          selectedItem.skipPenalties
        )
      }
    })

    // Compare for updates (action: "UPDATE")
    dataSelectedValuesTabQuestion?.forEach(selectedItem => {
      const matchedItem = dataAllFormApi?.find(item => item.checkListId === selectedItem.checkListId)
      if (matchedItem) {
        selectedItem.questionChoices.forEach(selectedChoice => {
          const matchedChoice = matchedItem.questionChoices.find(choice => choice.id === selectedChoice.id)
          if (matchedChoice && selectedChoice.penalties !== matchedChoice.penalties) {
            addUpdate(selectedChoice.keyCond, selectedChoice.penalties, 'UPDATE')
          }
        })
      }
    })

    // Compare for deletes (action: "DELETE")
    dataAllFormApi?.forEach(apiItem => {
      const matchedSelectedItem = dataSelectedValuesTabQuestion?.find(item => item.checkListId === apiItem.checkListId)
      if (!matchedSelectedItem) {
        processQuestionChoices(apiItem.questionChoices, 'DELETE', apiItem.skipKeyCond, apiItem.skipPenalties)
      } else {
        apiItem.questionChoices.forEach(apiChoice => {
          const matchedChoice = matchedSelectedItem.questionChoices.find(choice => choice.id === apiChoice.id)
          if (!matchedChoice) {
            addUpdate(apiChoice.keyCond, apiChoice.penalties, 'DELETE')
          }
        })
      }
    })

    return updates
  }

  const footerModelPrice = () => {
    if (!showEditBtn) {
      return (
        <div className="bg-base-25 pt-6 pb-4 px-6 flex gap-2 justify-between border-t-2">
          <Button
            cls="w-[179px]"
            colorScheme="primary"
            variant="outlined"
            onClick={() => {
              if (isChangeForm) {
                showCommonModal({
                  type: 'warning',
                  buttonType: 'horizontal-duo',
                  onClickNegativeButton: onCloseCommonModal,
                  onClose: onCloseCommonModal,
                  positiveButtonTxt: t('common.confirm-abandon'),
                  negativeButtonTxt: t('cancel'),
                  onClickPositiveButton: () => {
                    const dataCheckedFunctionFormApi = dataAllFormApi?.dataTabFunctionWork.filter(
                      itemFilter => itemFilter.checked
                    )
                    const dataCheckedQuestionsFormApi = dataAllFormApi?.dataTabQuestion.filter(
                      itemFilter => itemFilter.checked
                    )
                    setDataSelectedValuesTabWorkFuction(dataCheckedFunctionFormApi)
                    setDataSelectedValuesTabQuestion(dataCheckedQuestionsFormApi)
                    setDataSubmit(dataAllFormApi)
                    setShowEditBtn(true)
                    setIsChangeForm(false)
                    onCloseCommonModal()
                  },
                  title: t('model-master-table.leave-page-title'),
                  description: t('model-master-table.leave-page-description'),
                })
              } else {
                const dataCheckedFunctionFormApi = dataAllFormApi?.dataTabFunctionWork.filter(
                  itemFilter => itemFilter.checked
                )
                const dataCheckedQuestionsFormApi = dataAllFormApi?.dataTabQuestion.filter(
                  itemFilter => itemFilter.checked
                )
                setDataSelectedValuesTabWorkFuction(dataCheckedFunctionFormApi)
                setDataSelectedValuesTabQuestion(dataCheckedQuestionsFormApi)
                setDataSubmit(dataAllFormApi)
                setShowEditBtn(true)
                setIsChangeForm(false)
                setErrorTextBoxTag(null)
              }
            }}
          >
            {t('setting-operation-cost.cancel-button')}
          </Button>
          <Button
            cls="w-[179px]"
            colorScheme="primary"
            onClick={() => {
              setIsSubmit(true)
              onSave()
            }}
            isDisabled={dataSelectedValuesTabWorkFuction?.length === 0 || dataSelectedValuesTabQuestion?.length === 0}
          >
            {t('setting-operation-cost.save-button')}
          </Button>
        </div>
      )
    } else {
      return <></>
    }
  }

  const onCloseCommonModal = useCallback(() => {
    hideCommonModal()
    setModalType(null)
  }, [])

  const onSave = () => {
    // ---------------------- perpare data for model fucntion tab ----------------------------
    const outIdDataSelectedValuesTabWorkFuction = dataSelectedValuesTabWorkFuction?.map(itemMap => itemMap?.checkListId)
    const findItemBySelectTabWorkFuction = dataSubmitAll?.dataTabFunctionWork?.filter(
      itemFilter => outIdDataSelectedValuesTabWorkFuction?.includes(itemFilter.checkListId)
    )
    const dataCheckedInFuctionTabFormApi = dataAllFormApi?.dataTabFunctionWork.filter(itemfiter => itemfiter.checked)
    //-------------------------------------- END ---------------------------------------------

    // ---------------------- perpare data for model question tab ----------------------------
    const outIdDataSelectedValuesTabQuestion = dataSelectedValuesTabQuestion?.map(itemMap => itemMap?.checkListId)
    const findItemBySelectTabQuestion = dataSubmitAll?.dataTabQuestion?.filter(
      itemFilter => outIdDataSelectedValuesTabQuestion?.includes(itemFilter.checkListId)
    )
    const dataCheckedInQuestionTabFormApi = dataAllFormApi?.dataTabQuestion?.filter(itemfiter => itemfiter.checked)
    //-------------------------------------- END ---------------------------------------------

    const params: ISaveMasterModel = {
      companyId: dataAllFormApi?.companyId,
      modelKey: dataAllFormApi?.modelKey,
      modelMaster: {
        systemCode: dataSubmitAll?.systemCode,
        systemCodeList: dataSubmitAll?.systemCodeList,
        modelMasterGrades: dataSubmitAll?.modelMasterGrades,
      },
      modelMasterFunction: [
        ...compareDataForModelMasterFunction(findItemBySelectTabWorkFuction, dataCheckedInFuctionTabFormApi),
        ...compareDataForModelMasterQuestion(dataCheckedInQuestionTabFormApi, findItemBySelectTabQuestion),
      ],
    }

    let isCheckSystemCodePass = true
    let isCheckModelMasterGradesPass = true

    if (
      dataSubmitAll?.systemCodeList?.length === 0 ||
      dataSubmitAll?.systemCodeList === undefined ||
      dataSubmitAll?.systemCodeList === null
    ) {
      isCheckSystemCodePass = false
      scrollToFirstError('.tag-box')
      setErrorTextBoxTag(TagBoxErrorType.EMPTY_TAG)
    }

    const isValidGrades = validateModelPriceGrades(dataSubmitAll?.modelMasterGrades)

    if (!isTextBoxTagEmpty) {
      isCheckSystemCodePass = false
      scrollToFirstError('.tag-box')
      setErrorTextBoxTag(TagBoxErrorType.INPUT_FIELD_NOT_EMPTY)
    }

    if (!(isValidGrades.A && isValidGrades.B && isValidGrades.C && isValidGrades.D)) {
      isCheckModelMasterGradesPass = false
    }

    if (isCheckSystemCodePass && isCheckModelMasterGradesPass) {
      setErrorTextBoxTag(null)
      showCommonModal({
        type: 'info',
        buttonType: 'horizontal-duo',
        onClickNegativeButton: () => {
          hideCommonModal()
        },
        onClose: () => {
          hideCommonModal()
        },
        positiveButtonTxt: t('save'),
        negativeButtonTxt: t('cancel'),
        onClickPositiveButton: () => {
          saveModelMaster(params)
          hideCommonModal()
        },
        title: t('model-master-table.confirm-update-title'),
        description: t('model-master-table.confirm-update-description'),
      })
    }
  }

  return (
    <>
      <Loading isLoading={isLoadingAll} bgColor="bg-black/30" />
      <LayoutPage
        needBreadcrumb={false}
        fixFooter={footerModelPrice()}
        beforeLogoutFn={logoutFn => {
          if (isChangeForm) {
            showCommonModal({
              type: 'warning',
              buttonType: 'horizontal-duo',
              onClickNegativeButton: onCloseCommonModal,
              onClose: onCloseCommonModal,
              positiveButtonTxt: t('common.confirm-abandon'),
              negativeButtonTxt: t('cancel'),
              onClickPositiveButton: () => {
                setIsChangeForm(false)
                onCloseCommonModal()
                logoutFn()
              },
              title: t('model-master-table.leave-page-title'),
              description: t('model-master-table.leave-page-description'),
            })
          } else {
            logoutFn()
          }
        }}
      >
        <div className="py-0">
          {/* header bread crumb */}
          {showEditBtn ? (
            <div className="flex gap-2 mb-[10px]">
              <p
                className="text-lu4-regular mt-0.5 text-primary-500 cursor-pointer"
                onClick={() => router.push(`/${AdminMenuSlug.modelPrice}`)}
              >
                {t('layout.tab-model-price')}
              </p>
              <i className="w-6 h-6 icons ic-chevron-gray-right" />
              <div className="text-lu4-regular mt-0.5 text-base-700">{router.query.id}</div>
            </div>
          ) : (
            <div className="flex gap-2 mb-[10px] cursor-pointer">
              <p
                className="text-lu4-regular mt-0.5 text-primary-500 cursor-pointer"
                onClick={() => router.push(`/${AdminMenuSlug.modelPrice}`)}
              >
                {t('layout.tab-model-price')}
              </p>
              <i className="w-6 h-6 icons ic-chevron-gray-right" />
              <div
                className="text-lu4-regular mt-0.5 text-primary-500 cursor-pointer"
                onClick={() => {
                  const dataCheckedFunctionFormApi = dataAllFormApi?.dataTabFunctionWork?.filter(
                    itemFilter => itemFilter.checked
                  )
                  const dataCheckedQuestionsFormApi = dataAllFormApi?.dataTabQuestion?.filter(
                    itemFilter => itemFilter.checked
                  )
                  if (isChangeForm) {
                    showCommonModal({
                      type: 'warning',
                      buttonType: 'horizontal-duo',
                      onClickNegativeButton: onCloseCommonModal,
                      onClose: onCloseCommonModal,
                      positiveButtonTxt: t('common.confirm-abandon'),
                      negativeButtonTxt: t('cancel'),
                      onClickPositiveButton: () => {
                        const dataCheckedFunctionFormApi = dataAllFormApi?.dataTabFunctionWork.filter(
                          itemFilter => itemFilter.checked
                        )
                        const dataCheckedQuestionsFormApi = dataAllFormApi?.dataTabQuestion.filter(
                          itemFilter => itemFilter.checked
                        )
                        setDataSelectedValuesTabWorkFuction(dataCheckedFunctionFormApi)
                        setDataSelectedValuesTabQuestion(dataCheckedQuestionsFormApi)
                        setDataSubmit(dataAllFormApi)
                        setShowEditBtn(true)
                        setIsChangeForm(false)
                        onCloseCommonModal()
                      },
                      title: t('model-master-table.leave-page-title'),
                      description: t('model-master-table.leave-page-description'),
                    })
                  } else {
                    setDataSelectedValuesTabWorkFuction(dataCheckedFunctionFormApi)
                    setDataSelectedValuesTabQuestion(dataCheckedQuestionsFormApi)
                    setDataSubmit(dataAllFormApi)
                    setShowEditBtn(true)
                    setIsChangeForm(false)
                  }
                }}
              >
                {router.query.id}
              </div>
              <i className="w-6 h-6 icons ic-chevron-gray-right" />
              <div className="text-lu4-regular mt-0.5 text-base-700">{t('setting-operation-cost.edit-button')}</div>
            </div>
          )}
          {/* header section */}
          <div className="flex flex-row justify-between">
            <div>
              <p className="text-h4-bold text-base-700 ">
                {showEditBtn ? t('master-price.header') : t('setting-operation-cost.edit-button')}
              </p>
            </div>
            <div className="flex flex-row">
              {showEditBtn && (
                <Button
                  leftIcon={
                    <i
                      className="icons ic-edit-blue-outline mr-2"
                      style={{ width: '19px', height: '19px', marginTop: '-4px', marginRight: '11px' }}
                    />
                  }
                  colorScheme="primary"
                  variant="outlined"
                  onClick={() => {
                    setShowEditBtn(false)
                  }}
                  cls={`${PermissionMapCode.CMS_MODEL_MANAGE}-update-permission`}
                >
                  <>{t('setting-operation-cost.edit-button')}</>
                </Button>
              )}
            </div>
          </div>
          {/* header form section */}
          <div className="my-4 px-6 pt-6 pb-5 bg-white border border-[#E4E7EC] rounded-lg flex flex-col gap-4">
            <p className="text-h4-bold text-primary-500">
              {t('detail-after-sale-component.detail-section-header-section-product-detail')}
            </p>
            <div className="grid grid-cols-[auto_auto_auto] gap-6">{renderDetailProductForm()}</div>
          </div>

          <div className=" bg-white rounded-lg">
            <TabSelection
              onClickTab={status => {
                setTabValue(status)
                setFocusedIndex(null)
              }}
              currentValue={tabValue}
              data={[
                {
                  label: t('master-price.product-price'),
                  value: ModelPriceFunctionTab.PRODUCTPRICE,
                  children: (
                    <DetailPriceProductTab
                      showEditBtn={showEditBtn}
                      dataSubmitAll={dataSubmitAll}
                      setDataSubmit={setDataSubmit}
                      setIsChangeForm={setIsChangeForm}
                      focusedIndex={focusedIndex}
                      setFocusedIndex={setFocusedIndex}
                      isSubmit={isSubmit}
                      setIsSubmit={setIsSubmit}
                    />
                  ),
                  tabStyle: 'p-0 pb-0',
                },
                {
                  label: t('master-price.functionality'),
                  value: ModelPriceFunctionTab.FUNCTIONALITY,
                  children: (
                    <DetailFunctionTab
                      isLoadingTabFunctionWork={isLoadingTabFunctionWork}
                      setIsChangeForm={setIsChangeForm}
                      showEditBtn={showEditBtn}
                      dataSubmitAll={dataSubmitAll}
                      setDataSubmit={setDataSubmit}
                      dataSelectedValuesTabWorkFuction={dataSelectedValuesTabWorkFuction}
                      setDataSelectedValuesTabWorkFuction={setDataSelectedValuesTabWorkFuction}
                      focusedIndex={focusedIndex}
                      setFocusedIndex={setFocusedIndex}
                    />
                  ),
                  tabStyle: 'p-0 pb-0',
                },
                {
                  label: t('master-price.questions-about-the-machine'),
                  value: ModelPriceFunctionTab.QUESTIONSABOUTTHEMACHINE,
                  children: (
                    <DetailQuestionTab
                      showEditBtn={showEditBtn}
                      dataSubmitAll={dataSubmitAll}
                      setDataSubmit={setDataSubmit}
                      setIsChangeForm={setIsChangeForm}
                      dataSelectedValuesTabQuestion={dataSelectedValuesTabQuestion}
                      setDataSelectedValuesTabQuestion={setDataSelectedValuesTabQuestion}
                      focusedIndex={focusedIndex}
                      setFocusedIndex={setFocusedIndex}
                    />
                  ),
                  tabStyle: 'p-0 pb-0',
                },
              ]}
              headerCls="border-2 rounded-lg bg-base-25 border-[#eeeeee] !bg-opacity-100 z-[1]"
            />
          </div>
        </div>
      </LayoutPage>
    </>
  )
}

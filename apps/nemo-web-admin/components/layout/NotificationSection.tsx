import { FC, useEffect, useCallback, useState, useMemo } from 'react'
import { useRouter } from 'next/router'
import { useShallow } from 'zustand/react/shallow'
import { collection, onSnapshot, or, query, where, getFirestore, orderBy, limit } from 'firebase/firestore'
import { Drawer } from 'ui'
import { displayTime, formatMillisecondsToDateTime, isTimestampInCurrentDate } from '@/utils'
import { useTranslation } from '@/hooks'
import { JobStatus } from '@/services/models/jobs'
import { usePersistStore } from '@/stores/persistStore'
import { useUserStore } from '@/stores/userStore'
import { InboxType } from 'contracts'
import { AdminMenuSlug } from 'ui/src/models'
import {
  askForPermissionAndCallNotification,
  checkPermissionAndCallNotification,
  ICallNotiProp,
  ITypeNoti,
  PermissionAction,
  PermissionMapCode,
} from 'utils'
import { ISystemModalType, useSystemStore } from '@/stores/systemStore'

// -------- NOTIFICATION INTERFACE AND CONFIG -------- //
interface INotification {
  audience: string
  jobId: string
  deliveryOrderId: string
  status: JobStatus
  type: InboxType
  updatedAt: number
  remainVoucher?: number
  voucherValue?: number
  issueReportId: string
}

type InboxConfig = { icon: string; title: string; message: string; cls: string }

const inboxEachType: Partial<Record<InboxType, InboxConfig>> = {
  [InboxType.SHOP_FEEDBACK]: {
    icon: 'ic-comments',
    title: 'notification.feedback-title',
    message: 'notification.feedback-message',
    cls: 'bg-primary-50',
  },
  [InboxType.JOB_CREATE]: {
    icon: 'ic-hourglass-orange',
    title: 'notification.job-create-title',
    message: 'notification.job-create-message',
    cls: 'bg-warning-100',
  },
  [InboxType.DELIVERY_ORDER_REQUESTED]: {
    icon: 'ic-task-orange',
    title: 'notification.appointment-pending-title',
    message: 'notification.appointment-pending-message',
    cls: 'bg-warning-100',
  },
  [InboxType.DELIVERY_ORDER_SHIPPED]: {
    icon: 'ic-truck-orange',
    title: 'notification.in-transit-title',
    message: 'notification.in-transit-message',
    cls: 'bg-warning-100',
  },
  [InboxType.ISSUE_REPORT_REQUESTED]: {
    icon: 'ic-hourglass-orange',
    title: 'notification.issue-report-rejected-title',
    message: 'notification.issue-report-rejected-message',
    cls: 'bg-warning-100',
  },
  [InboxType.IMPORTED_VOUCHER_OUT_OF_STOCK]: {
    icon: 'ic-dedit-card-yellow', // mock waiting for po
    title: 'notification.voucher-out-of-stock-title', // mock waiting for po
    message: 'notification.voucher-out-of-stock-message', // mock waiting for po
    cls: 'bg-warning-100', // mock waiting for po
  },
}

// -------- INTERFACE -------- //
interface Props {
  companyId: string
  setHaveUnreadNotification: (status: boolean) => void
  openNotificationFromNavBar: boolean
  clearOpenNotificationFromNavBar: () => void
}

// -------- MAIN COMPONENT -------- //
export const NotificationSection: FC<Props> = ({
  companyId,
  setHaveUnreadNotification,
  openNotificationFromNavBar,
  clearOpenNotificationFromNavBar,
}) => {
  const { t } = useTranslation('common')
  const router = useRouter()

  // ----- Store ----- //
  const { currentUser } = useUserStore(
    useShallow(state => {
      return {
        currentUser: state.user,
      }
    })
  )

  const {
    showCommonModal,
    hideCommonModal,
    systemModalOpenType,
    setSystemModalOpenType,
    clearSystemModalOpenType,
    permissionConfig,
  } = useSystemStore()

  const { lastOpenedInboxInMilliseconds, setLastOpenedInboxInMilliseconds } = usePersistStore(
    useShallow(state => {
      return {
        lastOpenedInboxInMilliseconds: state.lastOpenedInboxInMilliseconds,
        setLastOpenedInboxInMilliseconds: state.setLastOpenedInboxInMilliseconds,
      }
    })
  )

  const { userKey, vendorType } = useMemo(() => {
    const { userKey, vendorType } = currentUser || { userKey: '' }
    return { userKey, vendorType }
  }, [currentUser])

  // ----- useState ----- //
  const [isMount, setMount] = useState<boolean>(false)
  const [notiData, setNotiData] = useState<any>(null)
  const [showInbox, setShowInbox] = useState<boolean>(false)
  const [lastClickOpenNotificationTime, setLastClickOpenNotificationTime] = useState<number>(0)
  const [notifications, setNotifications] = useState<INotification[]>([])

  // --- life cycle ---//
  useEffect(() => {
    if (userKey && lastOpenedInboxInMilliseconds[userKey]) {
      setLastClickOpenNotificationTime(lastOpenedInboxInMilliseconds[userKey])
    }

    const subscribeInbox = subscribeInboxNotification()
    if (subscribeInbox !== null) {
      return () => {
        subscribeInbox?.()
      }
    }
  }, [userKey, permissionConfig])

  useEffect(() => {
    if (showInbox) {
      // [NOTE] Get notification from last 50 messages and sort by updatedAt
      const queryInbox = getQueryInbox(50)

      if (queryInbox) {
        return onSnapshot(queryInbox, querySnapshot => {
          const documents: INotification[] = querySnapshot.docs.map(doc => ({ ...doc.data() }) as INotification)
          setNotifications(documents)
        })
      }
    }
  }, [showInbox])

  useEffect(() => {
    if (openNotificationFromNavBar) {
      openNotificationHandler(true)
      clearOpenNotificationFromNavBar()
    }
  }, [openNotificationFromNavBar])

  useEffect(() => {
    if (notiData !== null) {
      if (isMount) {
        const callNotiProp = getNotificationConfigFromNotiData(notiData)
        checkPermissionAndCallNotification(callNotiProp, type => {
          if ([ITypeNoti.granted, ITypeNoti.NoAccess].includes(type)) {
            setNotiData(null)
          } else if (type === ITypeNoti.AskForPermission) {
            setSystemModalOpenType(ISystemModalType.askForPermissionNotification)
          }
        })
      }
      setMount(true)
    }
  }, [notiData])

  useEffect(() => {
    if (systemModalOpenType === ISystemModalType.askForPermissionNotification) {
      const callNotiProp = getNotificationConfigFromNotiData(notiData)
      showCommonModal({
        type: 'warning',
        buttonType: 'single',
        onClose: onCloseCommonModalAskNotiPermission,
        onClickPositiveButton: () => {
          askForPermissionAndCallNotification(callNotiProp)
          onCloseCommonModalAskNotiPermission()
        },
        positiveButtonTxt: t('notification.ask-for-notification-permission-modal-button'),
        title: t('notification.ask-for-notification-permission-modal-title'),
        description: t('notification.ask-for-notification-permission-modal-description'),
      })
    }
  }, [systemModalOpenType])

  // ----- function: notification function ----- //
  const openNotificationHandler = (isOpenAction: boolean) => {
    const timestamp = new Date().getTime()
    setLastOpenedInboxInMilliseconds(userKey, timestamp)
    setHaveUnreadNotification(false)
    setShowInbox(isOpenAction)
    if (!isOpenAction) {
      setLastClickOpenNotificationTime(timestamp)
    }
  }

  const getQueryInbox = useCallback(
    (queryLimit: number) => {
      if (userKey && permissionConfig) {
        const queryPath = `company/${companyId}/inbox/admin/message`
        const inboxColRef = collection(getFirestore(), queryPath)
        const queryArray: any[] = []

        if (permissionConfig[PermissionMapCode.CMS_JOB_ALL]?.[PermissionAction.UPDATE]) {
          queryArray.push(where('audience', '==', `${PermissionMapCode.CMS_JOB_ALL}_UPDATE:${vendorType}`))
        }
        if (permissionConfig[PermissionMapCode.CMS_JOB_MY]?.[PermissionAction.UPDATE]) {
          queryArray.push(where('audience', '==', `${PermissionMapCode.CMS_JOB_MY}_UPDATE:${userKey}`))
        }
        if (permissionConfig[PermissionMapCode.CMS_DELIVERY_ORDER_BY_DO]?.[PermissionAction.VIEW]) {
          queryArray.push(where('audience', '==', `${PermissionMapCode.CMS_DELIVERY_ORDER_BY_DO}_VIEW`))
        }
        if (permissionConfig[PermissionMapCode.CMS_VOUCHER_MANAGE]?.[PermissionAction.UPLOAD]) {
          queryArray.push(where('audience', '==', `${PermissionMapCode.CMS_VOUCHER_MANAGE}_UPLOAD`))
        }
        if (permissionConfig[PermissionMapCode.CMS_ISSUE_REPORT]?.[PermissionAction.VIEW]) {
          queryArray.push(where('audience', '==', `${PermissionMapCode.CMS_ISSUE_REPORT}_VIEW`))
        }

        if (queryArray.length) {
          const queryInbox = query(inboxColRef, or(...queryArray), orderBy('updatedAt', 'desc'), limit(queryLimit))

          return queryInbox
        }
      }
      return null
    },
    [userKey, companyId, permissionConfig]
  )

  const subscribeInboxNotification = () => {
    // [NOTE] Get last notification from messages and sort by updatedAt
    const queryInbox = getQueryInbox(1)

    if (!queryInbox) return null

    return onSnapshot(queryInbox, querySnapshot => {
      if (
        querySnapshot.size > 0 &&
        (lastOpenedInboxInMilliseconds[userKey] ?? 0) < querySnapshot.docs[0].data().updatedAt
      ) {
        setHaveUnreadNotification(true)
        const data = querySnapshot.docs[0].data()
        setNotiData(data)
      } else {
        setMount(true)
      }
    })
  }

  const onClickInboxItem = useCallback(
    (notification: INotification) => {
      const { pathname, push } = router
      const { type, jobId, deliveryOrderId, issueReportId } = notification

      if (type === InboxType.SHOP_FEEDBACK) {
        push({
          pathname: `/${pathname.includes(AdminMenuSlug.allJob) ? AdminMenuSlug.allJob : AdminMenuSlug.myJob}/detail`,
          query: { id: jobId, comment: new Date().getTime().toString() },
        })
      } else if (type === InboxType.JOB_CREATE) {
        router.push({
          pathname: `/${pathname.includes(AdminMenuSlug.myJob) ? AdminMenuSlug.myJob : AdminMenuSlug.allJob}/detail`,
          query: { id: jobId },
        })
      } else if ([InboxType.DELIVERY_ORDER_REQUESTED, InboxType.DELIVERY_ORDER_SHIPPED].includes(type as InboxType)) {
        push({
          pathname: `/${AdminMenuSlug.appointmentStatus}/detail`,
          query: { id: deliveryOrderId },
        })
      } else if (type === InboxType.ISSUE_REPORT_REQUESTED) {
        push({
          pathname: `/${AdminMenuSlug.issueReport}/detail`,
          query: { id: issueReportId, fromNotification: true },
        })
      } else if (type === InboxType.IMPORTED_VOUCHER_OUT_OF_STOCK) {
        router.push({ pathname: `/${AdminMenuSlug.uploadVoucher}`, query: { fromNotification: true } })
      }

      openNotificationHandler(false)
    },
    [router.pathname]
  )

  const onCloseCommonModalAskNotiPermission = useCallback(() => {
    setNotiData(null)
    clearSystemModalOpenType()
    hideCommonModal()
  }, [])

  // ----- Sub component ----- //
  const renderNotificationItem = useCallback(
    (notification: INotification, lastClickOpenNotificationTime: number) => {
      const { updatedAt, jobId, deliveryOrderId, type, issueReportId, remainVoucher, voucherValue } = notification
      const { title, message, cls, icon } = inboxEachType[type] ?? { title: '', message: '' }
      const messageId = jobId || deliveryOrderId || issueReportId
      const showRedDot = lastClickOpenNotificationTime < updatedAt || !lastClickOpenNotificationTime
      const timeStamp = isTimestampInCurrentDate(updatedAt)
        ? displayTime(new Date(updatedAt))
        : formatMillisecondsToDateTime(updatedAt)

      return (
        <div
          key={updatedAt}
          className="flex p-4 border-b hover:cursor-pointer"
          onClick={() => onClickInboxItem(notification)}
        >
          <div className={`relative ${cls} rounded-full p-[10px] w-[44px] h-[44px] mr-4`}>
            <i className={`icons ${icon}`} />
            {showRedDot && (
              <span
                className={`dotted-notification ${'inline-block bg-negative-500 rounded-full min-w-[10px] min-h-[10px] absolute bottom-0 right-0'}`}
              />
            )}
          </div>
          <div className="flex flex-col">
            <span className="mb-1 text-t5-semi-bold">{t(title, { remainVoucher, voucherValue })}</span>
            <span className="mb-1 text-b5-regular">{t(message, { messageId })}</span>
            <span className="text-b6-regular text-base-400">{timeStamp}</span>
          </div>
        </div>
      )
    },
    [router.pathname]
  )

  const getNotificationConfigFromNotiData = useCallback((notiData: INotification): ICallNotiProp => {
    const { jobId, deliveryOrderId, type, issueReportId, remainVoucher, voucherValue } = notiData
    const { title, message } = inboxEachType[type] ?? { title: '', message: '' }
    const messageId = issueReportId || deliveryOrderId || jobId
    return { title: t(title, { remainVoucher, voucherValue }), body: t(message, { messageId }), window }
  }, [])
  // ----- main component ----- //
  return (
    <Drawer title={t('notification.notification')} isOpen={showInbox} onClose={() => openNotificationHandler(false)}>
      {!notifications.length ? (
        <div className="flex flex-col items-center justify-center h-full">
          <div className="images img-empty-notification w-[300px] h-[225px]"></div>
          <span className="text-t2-semi-bold">{'คุณยังไม่มีการแจ้งเตือนใหม่'}</span>
        </div>
      ) : (
        notifications.map(notification =>
          Object.keys(inboxEachType).includes(notification.type) ? (
            renderNotificationItem(notification, lastClickOpenNotificationTime)
          ) : (
            <div key={notification.updatedAt}></div>
          )
        )
      )}
    </Drawer>
  )
}

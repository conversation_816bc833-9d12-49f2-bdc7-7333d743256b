import { FC, useEffect, useMemo, useState } from 'react'
import { Button, DropdownSimple } from 'ui'
import { Tooltip, TooltipProps } from '@material-tailwind/react'
import { offset } from '@material-tailwind/react/types/components/popover'
import 'survey-core/defaultV2.min.css'

import { useTranslation } from '@/hooks'
import { ModelMasterGrade } from '@/services/models/modelMaster'
import { formatPrice, getIsCancelStatus, defaultValue } from '@/utils'
import { useUserStore } from '@/stores/userStore'

export interface EstimatePrice {
  grade: string
  minPrice: number
  maxPrice: number
}

const offsetTooltip: { [key: string]: offset } = {
  top: { mainAxis: 5 },
  'top-end': { crossAxis: 5, mainAxis: 5 },
}

const PriceFooterTooltip: FC<{
  children: React.ReactNode
  content: React.ReactNode
  open?: boolean
  placement?: TooltipProps['placement']
}> = ({ children, content, open, placement = 'top' }) => {
  return (
    <Tooltip content={content} open={open} placement={placement} offset={offsetTooltip[placement] || { mainAxis: 5 }}>
      {children}
    </Tooltip>
  )
}

const getGrade = (price: number, gradeList: ModelMasterGrade[] | undefined): EstimatePrice => {
  if (price <= 0 || !gradeList) {
    return {
      grade: '-',
      maxPrice: Number(1),
      minPrice: Number(0),
    }
  }
  const modelMasterGrades = gradeList.sort((g1, g2) => Number(g2.purchasePrice) - Number(g1.purchasePrice))

  for (let i = 0; i < modelMasterGrades.length; i++) {
    if (
      price >= Number(modelMasterGrades[i].purchasePrice) ||
      price >= Number(modelMasterGrades[i + 1]?.purchasePrice || '1') + 1
    ) {
      return {
        grade: modelMasterGrades[i].grade,
        maxPrice: Number(modelMasterGrades[i].purchasePrice),
        minPrice: Number(defaultValue(modelMasterGrades[i + 1]?.purchasePrice, '0')) + 1,
      }
    }
  }

  const lastGrade = modelMasterGrades.find(modelMaster => modelMaster.grade === 'D')
  return {
    grade: defaultValue(lastGrade?.grade, 'D'),
    minPrice: 1,
    maxPrice: Number(defaultValue(lastGrade?.purchasePrice, '1')),
  }
}

type State = 'pending' | 'owner' | 'not-owner' | 'estimated' | 'cancelled' | undefined

interface Props {
  footerState?: State
  dataAtMount?: any
  calculatedPrice: number
  tick?: number
  onSubmit?: (price: number, grade: string) => void
  getPrice?: (price: number, isManual: boolean) => void
  permissionEdit?: boolean
}

export const PriceFooterKF: FC<Props> = ({
  footerState,
  dataAtMount,
  calculatedPrice,
  tick = 100,
  onSubmit,
  getPrice,
  permissionEdit,
}) => {
  const { t } = useTranslation('common')
  const { user } = useUserStore()
  const [price, setPrice] = useState<number>(calculatedPrice && calculatedPrice > 0 ? calculatedPrice : 0)
  const [selectedGrade, setSelectedGrade] = useState<ModelMasterGrade | null>(null)
  const [gradeDetail, setGradeDetail] = useState<EstimatePrice>()
  const [isClickTick, setIsClickTick] = useState<boolean>(false)
  const [disablePlus, setDisablePlus] = useState<boolean>(true)
  const [disableMinus, setDisableMinus] = useState<boolean>(true)

  const state: State = useMemo(() => {
    if (footerState) {
      return footerState
    }
    if (user?.userKey && dataAtMount) {
      if (!getIsCancelStatus(dataAtMount.status)) {
        if (!dataAtMount.adminUserKey) {
          return 'pending'
        } else if (dataAtMount.suggestedPrice == 0) {
          return dataAtMount.adminUserKey === user.userKey && permissionEdit ? 'owner' : 'not-owner'
        } else if (dataAtMount.suggestedPrice > 0) {
          return 'estimated'
        }
      }
      return dataAtMount.suggestedPrice == 0 ? 'cancelled' : 'estimated'
    }
    return undefined
  }, [user, dataAtMount, permissionEdit])

  const [gradeList, maxMinPrice] = useMemo(() => {
    let maxMin = { max: 1, min: 1 }
    if (dataAtMount) {
      const gradeList = dataAtMount.modelTemplate?.modelMasterGrades as ModelMasterGrade[]
      if (gradeList) {
        const modelMasterGrades = gradeList.sort((g1, g2) => Number(g2.purchasePrice) - Number(g1.purchasePrice))
        maxMin = { max: Number(modelMasterGrades[0].purchasePrice || '1'), min: 1 }
      }
      return [gradeList, maxMin]
    }
    return [undefined, maxMin]
  }, [dataAtMount])

  // Convert grades to dropdown choices
  const gradeChoices = useMemo(() => {
    if (!gradeList) return []
    return gradeList.map(grade => ({
      value: grade.grade,
      text: `${grade.grade} - ${formatPrice(Number(grade.purchasePrice), 2)}`,
      data: grade,
    }))
  }, [gradeList])

  useEffect(() => {
    if (!isClickTick) {
      setPrice(calculatedPrice)
    }
  }, [calculatedPrice])

  useEffect(() => {
    if (state === 'owner' && dataAtMount?.suggestedPriceKeep) {
      setIsClickTick(true)
      setPrice(dataAtMount.suggestedPriceKeep)
    } else {
      setIsClickTick(false)
    }
  }, [dataAtMount])

  // Auto-select grade based on price
  useEffect(() => {
    if (gradeList && price > 0) {
      const gradeDetail = getGrade(price, gradeList)
      const matchingGrade = gradeList.find(g => g.grade === gradeDetail.grade)
      if (matchingGrade && (!selectedGrade || selectedGrade.grade !== matchingGrade.grade)) {
        setSelectedGrade(matchingGrade)
      }
    }
  }, [price, gradeList])

  // Set default grade on mount
  useEffect(() => {
    if (gradeList && gradeList.length > 0 && !selectedGrade) {
      const defaultGrade = getGrade(calculatedPrice || 0, gradeList)
      const matchingGrade = gradeList.find(g => g.grade === defaultGrade.grade)
      if (matchingGrade) {
        setSelectedGrade(matchingGrade)
      }
    }
  }, [gradeList, calculatedPrice])

  useEffect(() => {
    if (getPrice) {
      getPrice(price, isClickTick)
    }

    // Update grade detail based on price
    if (gradeList) {
      setGradeDetail(getGrade(price, gradeList))
    }
  }, [price, isClickTick, gradeList])

  useEffect(() => {
    setDisablePlus(price >= maxMinPrice.max)
    setDisableMinus(price <= maxMinPrice.min)
  }, [price, maxMinPrice])

  const handleGradeChange = (gradeChoice: any) => {
    const grade = gradeChoice.data as ModelMasterGrade
    setSelectedGrade(grade)
    setPrice(Number(grade.purchasePrice))
    setIsClickTick(true)
  }

  return state && dataAtMount ? (
    <footer className="bg-white flex-col w-[100%]">
      <div className="flex flex-row items-end justify-between px-6 pt-4 pb-6">
        <span className="text-h4-bold text-primary-500">{t('jobs-management.estimate-product-price')}</span>
        <div className="flex">
          {/* grade dropdown part */}
          {state !== 'estimated' && (
            <div className="flex flex-col mr-4 justify-end">
              <span className="text-b4-regular text-base-500">{t('jobs-management.grade')}</span>
              {state === 'owner' ? (
                <div className="min-w-[120px]">
                  <DropdownSimple
                    selected={
                      selectedGrade
                        ? {
                            value: selectedGrade.grade,
                            text: `${selectedGrade.grade} - ${formatPrice(Number(selectedGrade.purchasePrice), 2)}`,
                          }
                        : { value: '', text: '' }
                    }
                    setSelection={handleGradeChange}
                    choices={gradeChoices}
                    valueField="value"
                    textField="value"
                    t={t}
                    isAutoCalculatePosition
                  />
                </div>
              ) : (
                <span className="text-t3-semi-bold text-base-700">{selectedGrade?.grade || '-'}</span>
              )}
            </div>
          )}

          <div className="flex flex-row">
            {/* price and edit price part */}
            {t('common.jobs-management.buying-price')}
            <div className="text-h2-bold flex flex-row items-end">{formatPrice(price, 2)}</div>
          </div>
        </div>
      </div>
      {/* submit button */}
      {state === 'owner' && (
        <div className={`flex justify-end pt-[0.9rem] pb-6 px-6 border-t`}>
          <Button
            colorScheme="primary"
            variant="filled"
            size="m"
            cls={`h-[48px] w-[168px]`}
            isDisabled={price <= 0 || !selectedGrade}
            onClick={onSubmit && selectedGrade ? () => onSubmit(price, selectedGrade.grade) : undefined}
          >
            {t('confirm')}
          </Button>
        </div>
      )}
    </footer>
  ) : (
    <footer className="bg-white flex-col w-[100%]">
      <div className="flex flex-row items-end justify-between px-6 pt-4 pb-6">
        <span className="text-h4-bold text-primary-500">{t('jobs-management.estimate-product-price')}</span>
        <span className="text-h2-bold text-base-700">{t('jobs-management.no-data-to-display')}</span>
      </div>
    </footer>
  )
}

import JobTable from '@/components/JobTable'
import { LayoutPage } from '@/components/layout'
import { Filter } from '@/components/filter'
import { withAuth } from '@/hocs'
import { useTranslation } from '@/hooks'
import { useListQueryContext } from '@/hooks/useListQueryContext'
import { useJobsStore } from '@/stores/jobsStore'
import router from 'next/router'
import { useEffect, useState } from 'react'
import { Button, GroupSection, UnderlineTabs } from 'ui'
import { useShallow } from 'zustand/react/shallow'
import { jobsFilterForm } from '@/survey-forms/filters'
import { defaultValue } from '@/utils'

function IndexPage() {
  const { t } = useTranslation('common')

  const [quickSearch, setQuickSearch] = useState<string>('')
  const [isShowFilter, setIsShowFilter] = useState<boolean>(false)
  const [filter, setFilter] = useState<{ [key: string]: string }>({})
  const [isSearch, setIsSearch] = useState<boolean>(false)

  const onChangeAdvancedSearch = (value: string) => {
    onAddQuery({ status: value })
  }

  const { fetchJobs, pagination, jobCount } = useJobsStore(
    useShallow(state => ({
      pagination: state.pagination,
      jobCount: state.jobCount,
      fetchJobs: state.fetchJobs,
    }))
  )
  const { getQueryContext, onAddQuery } = useListQueryContext(router, pagination, setQuickSearch)

  const searchJobs = (filterValue: { [key: string]: string } | undefined) => {
    const search: { [key: string]: string } = {}
    for (const key in filterValue) {
      if (filterValue[key] !== 'none') {
        search[key] = filterValue[key]
      }
    }
    setFilter(search)
    onAddQuery({ ...getQueryContext(), page: '1' })
    setIsSearch(true)
  }

  const clearJobs = (filterModel: any) => {
    setFilter({})
    filterModel.clear()
    onAddQuery({ ...getQueryContext(), page: '1' })
    setIsSearch(false)
  }

  const [tabValue, setTabValue] = useState<string>(getQueryContext()['status']?.toString() ?? '')

  useEffect(() => {
    //console.log('my--->', router.query)
    const queryContext = { ...getQueryContext({ myJob: 'true', isCheckVendorType: 'true' }) }
    queryContext.status = defaultValue(queryContext['status']?.toString(), 'estimating')
    setTabValue(queryContext.status)
    if (router.query.status === undefined) {
      onAddQuery({ status: 'estimating' })
    } else {
      fetchJobs({ ...queryContext, ...filter })
    }
  }, [router.query])

  return (
    <LayoutPage
      pageTitle={{
        title: t('layout.tab-my-jobs'),
      }}
    >
      <div className="flex flex-col justify-center items-center mt-6">
        <GroupSection
          title={t('product-list')}
          titleCls="!text-base-700 text-t2-semi-bold"
          sectionAction={
            <Button colorScheme="primary" variant="outlined" onClick={() => setIsShowFilter(!isShowFilter)}>
              {isShowFilter ? (
                <>
                  <i className="icons ic-close-blue mr-2"></i>
                  {t('common.filter.close-filter')}
                </>
              ) : (
                <>
                  <i className="icons ic-filter-blue mr-2"></i>
                  {t('common.filter.filter')}
                </>
              )}
            </Button>
          }
          isRounded
          cls="w-full h-max pb-0 pt-4"
        >
          <div className="mt-4">
            <Filter
              show={isShowFilter}
              filterForm={jobsFilterForm}
              onClickSearch={result => searchJobs(result)}
              onClickClear={clearJobs}
            />
            <UnderlineTabs
              onClickTab={(status: string) => {
                onChangeAdvancedSearch(status)
              }}
              currentValue={tabValue ?? ''}
              data={[
                {
                  label: t('common.status.estimating'),
                  value: 'estimating',
                  children: <JobTable mode="estimating" isSearch={isSearch} />,
                  tabStyle: 'p-0 pb-4',
                  notification: jobCount ? jobCount['my-estimating'] : undefined,
                },
                {
                  label: t('common.status.estimate-success'),
                  value: 'estimate-success',
                  children: <JobTable mode="estimate-success" isSearch={isSearch} />,
                  tabStyle: 'p-0 pb-4',
                  notification: 0,
                },
                {
                  label: t('common.status.success'),
                  value: 'success',
                  children: <JobTable mode="success" isSearch={isSearch} />,
                  tabStyle: 'p-0 pb-4',
                  notification: 0,
                },
              ]}
            />
          </div>
        </GroupSection>
      </div>
    </LayoutPage>
  )
}

export default withAuth(IndexPage)

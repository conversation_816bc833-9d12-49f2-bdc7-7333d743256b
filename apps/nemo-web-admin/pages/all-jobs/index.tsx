import JobTable from '@/components/JobTable'
import { LayoutPage } from '@/components/layout'
import { Filter } from '@/components/filter'
import { withAuth } from '@/hocs'
import { useTranslation } from '@/hooks'
import { useListQueryContext } from '@/hooks/useListQueryContext'
import { useJobsStore } from '@/stores/jobsStore'
import router from 'next/router'
import { useEffect, useState } from 'react'
import { Button, GroupSection, UnderlineTabs, DataTableItem } from 'ui'
import { useShallow } from 'zustand/react/shallow'
import { jobsFilterForm } from '@/survey-forms/filters'
import { useSystemStore } from '@/stores/systemStore'
import { PermissionAction, PermissionMapCode } from 'utils'
import { AdminMenuSlug, CommonModalContext } from 'ui/src/models'

const modalByType = {
  success: {
    type: 'success',
    title: 'assign-job.success-title',
    description: 'assign-job.success-desc',
    buttonType: 'vertical-duo',
    positiveButtonTxt: 'assign-job.make-an-estimate',
  },
  '101003': {
    type: 'warning',
    title: 'assign-job.fail-cancel-title',
    description: 'assign-job.fail-cancel-desc',
    buttonType: 'single',
    positiveButtonTxt: 'agree',
  },
  default: {
    type: 'warning',
    title: 'assign-job.fail-title',
    description: 'assign-job.fail-desc',
    buttonType: 'single',
    positiveButtonTxt: 'agree',
  },
} as const
type IType = keyof typeof modalByType

function IndexPage() {
  const { t } = useTranslation('common')

  const [quickSearch, setQuickSearch] = useState<string>('')
  const [isShowFilter, setIsShowFilter] = useState<boolean>(false)
  const [filter, setFilter] = useState<{ [key: string]: string }>({})
  const [isSearch, setIsSearch] = useState<boolean>(false)

  const onChangeAdvancedSearch = (value: string) => {
    onAddQuery({ status: value })
  }

  const { showCommonModal, hideCommonModal } = useSystemStore()

  const { fetchJobs, pagination, jobCount, fetchJobCount, assignJob } = useJobsStore(
    useShallow(state => ({
      pagination: state.pagination,
      jobCount: state.jobCount,
      fetchJobs: state.fetchJobs,
      fetchJobCount: state.fetchJobCount,
      assignJob: state.assignJob,
    }))
  )
  const { getQueryContext, onAddQuery } = useListQueryContext(router, pagination, setQuickSearch)

  const searchJobs = (filterValue: { [key: string]: string } | undefined) => {
    const search: { [key: string]: string } = {}
    for (const key in filterValue) {
      if (filterValue[key] !== 'none') {
        search[key] = filterValue[key]
      }
    }
    setFilter(search)
    onAddQuery({ ...getQueryContext(), page: '1' })
    setIsSearch(true)
  }

  const clearJobs = (filterModel: any) => {
    setFilter({})
    filterModel.clear()
    onAddQuery({ ...getQueryContext(), page: '1' })
    setIsSearch(false)
  }

  const modalOpen = (isSuccess: boolean, jobId?: string, errorCode?: string) => {
    const onClose = () => {
      fetchJobs({ ...getQueryContext(), ...filter })
      fetchJobCount()
      hideCommonModal()
    }
    let type: IType = 'default'
    if (isSuccess) {
      type = 'success'
    } else if (errorCode === '101003') {
      type = '101003'
    }
    const modalConfig = modalByType[type]
    const modalProp: CommonModalContext = {
      type: modalConfig.type,
      title: t(modalConfig.title),
      description: t(modalConfig.description),
      buttonType: modalConfig.buttonType,
      positiveButtonTxt: t(modalConfig.positiveButtonTxt),
      onClose,
      onClickPositiveButton: onClose,
    }
    if (type === 'success') {
      modalProp.onClickPositiveButton = () => {
        router.push(`/${AdminMenuSlug.allJob}/detail?id=${jobId}`)
        fetchJobCount()
        hideCommonModal()
      }
      modalProp.negativeButtonTxt = t('assign-job.accept-other-jobs')
      modalProp.onClickNegativeButton = onClose
    } else if (type === '101003') {
      modalProp.icon = 'ic-warning-canceled-estimated'
    }

    showCommonModal(modalProp)
  }

  const onClickToEstimatePage = async (item: DataTableItem) => {
    if (item.status === '10_QUOTE_REQUESTED') {
      const result = await assignJob(item.jobId, error => {
        modalOpen(false, item.jobId, error.body?.code)
      })
      if (result) {
        modalOpen(true, item.jobId)
      }
    }
  }

  const [tabValue, setTabValue] = useState<string>(getQueryContext()['status']?.toString() ?? '')

  useEffect(() => {
    const queryContext = { ...getQueryContext({ isCheckVendorType: 'true' }) }
    queryContext.status = queryContext['status']?.toString() ?? 'pending'
    setTabValue(queryContext.status)

    if (router.query.status === undefined) {
      onAddQuery({ status: 'pending' })
    } else {
      fetchJobs({ ...queryContext, ...filter })
    }
  }, [router.query])
  return (
    <LayoutPage
      pageTitle={{
        title: t('jobs-management.estimate-price'),
      }}
    >
      <div className="flex flex-col justify-center items-center mt-6">
        <GroupSection
          title={t('product-list')}
          titleCls="!text-base-700 text-t2-semi-bold"
          isRounded
          sectionAction={
            <Button colorScheme="primary" variant="outlined" onClick={() => setIsShowFilter(!isShowFilter)}>
              {isShowFilter ? (
                <>
                  <i className="icons ic-close-blue mr-2"></i>
                  {t('common.filter.close-filter')}
                </>
              ) : (
                <>
                  <i className="icons ic-filter-blue mr-2"></i>
                  {t('common.filter.filter')}
                </>
              )}
            </Button>
          }
          cls="w-full h-max pb-0 pt-4"
        >
          <div className="mt-4">
            <Filter
              show={isShowFilter}
              filterForm={jobsFilterForm}
              onClickSearch={result => searchJobs(result)}
              onClickClear={clearJobs}
            />
            <UnderlineTabs
              onClickTab={(status: string) => {
                onChangeAdvancedSearch(status)
              }}
              currentValue={tabValue}
              data={[
                {
                  label: t('common.status.pending'),
                  value: 'pending',
                  children: (
                    <JobTable
                      mode="pending"
                      isSearch={isSearch}
                      onClickAction={onClickToEstimatePage}
                      actionPermission={{ permission: PermissionMapCode.CMS_JOB_ALL, action: PermissionAction.UPDATE }}
                    />
                  ),
                  tabStyle: 'p-0 pb-4',
                  notification: jobCount ? jobCount['all-pending'] : undefined,
                },
                {
                  label: t('common.status.estimating'),
                  value: 'estimating',
                  children: <JobTable mode="estimating" isSearch={isSearch} />,
                  tabStyle: 'p-0 pb-4',
                  notification: jobCount ? jobCount['all-estimating'] : undefined,
                },
                {
                  label: t('common.status.estimate-success'),
                  value: 'estimate-success',
                  children: <JobTable mode="estimate-success" isSearch={isSearch} />,
                  tabStyle: 'p-0 pb-4',
                  notification: 0,
                },
                {
                  label: t('common.status.success'),
                  value: 'success',
                  children: <JobTable mode="success" isSearch={isSearch} />,
                  tabStyle: 'p-0 pb-4',
                  notification: 0,
                },
              ]}
            />
          </div>
        </GroupSection>
      </div>
    </LayoutPage>
  )
}

export default withAuth(IndexPage)

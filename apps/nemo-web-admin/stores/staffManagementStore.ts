import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
// Import Service
import { NemoService } from '@/services/repositories'
// Import Util
import { wrapInvocation } from '@/utils'
import { ConnectionState, ConnectionStateResult, Sorting } from 'utils'
import { IPagination, defaultPagination } from 'ui'
import { ISystemModalType, useSystemStore } from './systemStore'
import { ISubmitDataFrontshopRole } from '@/components/staffManage/detail/pageView'
import { IStaffUpdateBody } from '@/services/models/staff'

export enum ICommonModalType {
  errorUpload = 'ERROR_UPLOAD',
  unexpectedError = 'ERROR_UNEXCEPTED',
}
export enum IToastType {
  successUpload = 'SUCCESS_UPLOAD',
  updateSuccess = 'UPDATE_SUCCESS',
}
export const defaultStaffManagerTableSorting: Sorting = { by: 'userKey', direction: 'asc' }

export const defaultStaffFilterData = {
  staffName: '',
  branch: '',
}

export type IStaffFilterData = typeof defaultStaffFilterData

export type IRoleNameById = {
  FRONTSHOP: { [key: string]: string }
  CMS: { [key: string]: string }
}

export interface StaffManagementState {
  modalOpenType: ICommonModalType | null
  toastOpenType: IToastType | null
  staffTableData: any[]
  roleNameById: IRoleNameById | null
  staffTableSorting: Sorting
  staffTablePagination: IPagination
  staffTableFilter: IStaffFilterData
  staffConnectionState: ConnectionStateResult
  fetchStaffTableData: (allFilter?: any) => Promise<void>
  updateStaff: (props: { id: string; body: IStaffUpdateBody; afterSuccessFn: (id: string) => void }) => Promise<void>
  getRoleConfig: () => Promise<void>
  getStaffConfig: (userId: string, withBranchName: boolean, onSuccess: (data: any) => void) => Promise<void>
  setStaffTableSorting: (by: string | null, direction: 'asc' | 'desc' | undefined) => void
  setStaffTablePagination: (value: number, field: keyof IPagination) => void
  setStaffTableFilter: (filter: IStaffFilterData) => void
  uploadStaffFile: (file: any) => Promise<void>
  onLeaveStaffManagePage: () => void
  onLeaveStaffDetailPageClear: () => void
  clearStaffAllStoreFilter: () => void
  clearStaffTableSorting: () => void
  clearModalOpenType: () => void
  clearToastOpenType: () => void
}

export const useStaffManagementStore = create<StaffManagementState>()(
  devtools(
    (set, get) => ({
      modalOpenType: null,
      toastOpenType: null,
      staffTableData: [],
      roleNameById: null,
      staffTableSorting: { ...defaultStaffManagerTableSorting },
      staffTablePagination: { ...defaultPagination },
      staffTableFilter: { ...defaultStaffFilterData },
      staffConnectionState: ConnectionState.Loading(),
      fetchStaffTableData: async (allFilter?: any) => {
        await wrapInvocation({
          callBack: async () => {
            const { sorting, search, ...props } = allFilter
            const queryContext = { ...props }
            if (sorting?.by) {
              queryContext.orderBy = `${sorting.by} ${sorting.direction}`
            }
            if (search) {
              queryContext.searchEmployee = search
            }
            const result = await NemoService.repositories.user.fetchUsers(queryContext)
            set({
              staffTableData: result.data,
              staffTablePagination: result.pagination,
            })
          },
          handleError: {
            onError: (error: any) => {
              console.log(error)
              set({
                staffConnectionState: ConnectionState.Error(error),
              })
            },
          },
          handleLoading: {
            onLoading: (isLoading: boolean) => {
              set({
                staffConnectionState: isLoading ? ConnectionState.Loading() : ConnectionState.Success(),
              })
            },
            skipSystemLoading: true,
          },
        })
      },
      updateStaff: async ({ id, body, afterSuccessFn }) => {
        await wrapInvocation({
          callBack: async () => {
            await NemoService.repositories.user.updateUser(id, body)
            set({
              toastOpenType: IToastType.updateSuccess,
            })
            afterSuccessFn(id)
          },
          handleError: {
            onError: (error: any) => {
              useSystemStore.getState().setSystemModalOpenType(ISystemModalType.unexpectedError)
            },
          },
        })
      },
      getRoleConfig: async () => {
        await wrapInvocation({
          callBack: async () => {
            const resp = await NemoService.repositories.user.getRoleConfig()

            const frontshop = resp.filter(role => role.type === 'FRONTSHOP')
            const cms = resp.filter(role => role.type === 'CMS')

            set({
              roleNameById: {
                FRONTSHOP: frontshop.reduce(
                  (acc, cur) => {
                    acc[cur.roleId] = cur.roleName
                    return acc
                  },
                  {} as { [key: string]: string }
                ),
                CMS: cms.reduce(
                  (acc, cur) => {
                    acc[cur.roleId] = cur.roleName
                    return acc
                  },
                  {} as { [key: string]: string }
                ),
              },
            })
          },
          handleError: {
            onError: (error: any) => {
              useSystemStore.getState().setSystemModalOpenType(ISystemModalType.unexpectedError)
            },
          },
          handleLoading: {
            skipSystemLoading: false,
          },
        })
      },
      getStaffConfig: async (userId: string, withBranchName: boolean, onSuccess: (data: any) => void) => {
        await wrapInvocation({
          callBack: async () => {
            const { roles, ...detailStaff } = await NemoService.repositories.user.getStaffConfig(userId)
            const mountData = {
              ...detailStaff,
              frontshop: roles.frontshop.map(role => {
                const roleData: ISubmitDataFrontshopRole = { roleId: role.roleId, branchId: role.branch.branchId }
                if (withBranchName) {
                  roleData.branchName = role.branch.branchName
                }
                return roleData
              }),
              cms: roles.cms.map(role => role.roleId),
            }
            onSuccess(mountData)
          },
          handleError: {
            onError: (error: any) => {
              useSystemStore.getState().setSystemModalOpenType(ISystemModalType.unexpectedError)
            },
          },
          handleLoading: {
            skipSystemLoading: false,
          },
        })
      },
      setStaffTableSorting: (by: string | null, direction: 'asc' | 'desc' | undefined) => {
        set({ staffTableSorting: { by, direction } })
      },
      setStaffTablePagination: (value: number, field: keyof IPagination) => {
        set({ staffTablePagination: { ...get().staffTablePagination, [field]: value } })
      },
      setStaffTableFilter: (value: IStaffFilterData) => {
        set({ staffTableFilter: value })
      },
      uploadStaffFile: async (file: any) => {
        await wrapInvocation({
          callBack: async () => {
            await NemoService.repositories.upload.uploadUsers(file.fileName, file)
            set({
              toastOpenType: IToastType.successUpload, // call toast success
            })
          },
          handleError: {
            onError: (error: any) => {
              console.log(error)
              if (error.httpStatus === 400) {
                set({ modalOpenType: ICommonModalType.errorUpload }) // call modal error
              } else {
                set({ modalOpenType: ICommonModalType.unexpectedError })
              }
            },
          },
          handleLoading: {
            skipSystemLoading: false,
          },
        })
      },
      onLeaveStaffManagePage: () => {
        set({
          modalOpenType: null,
          toastOpenType: null,
          staffTableData: [],
          staffTablePagination: { ...defaultPagination },
          staffTableFilter: { ...defaultStaffFilterData },
          staffTableSorting: { ...defaultStaffManagerTableSorting },
        })
      },
      onLeaveStaffDetailPageClear: () => {
        set({
          roleNameById: null,
        })
      },
      clearStaffAllStoreFilter: () => {
        set({
          staffTablePagination: { ...defaultPagination },
          staffTableFilter: { ...defaultStaffFilterData },
        })
      },
      clearStaffTableSorting: () => {
        set({
          staffTableSorting: { ...defaultStaffManagerTableSorting },
        })
      },
      clearModalOpenType: () => {
        set({ modalOpenType: null })
      },
      clearToastOpenType: () => {
        set({ toastOpenType: null })
      },
    }),
    {
      name: 'staff-management-storage',
    }
  )
)
